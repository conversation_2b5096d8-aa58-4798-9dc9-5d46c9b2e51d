# AWS 设置指南

本指南提供了为 Bedrock Agent 应用程序设置所需 AWS 服务的分步说明。

## 📋 前置要求

- 具有管理员访问权限的 AWS 账户
- 已安装并配置的 AWS CLI（可选但推荐）
- 对 AWS IAM 和服务的基本了解

## 🔧 必需的 AWS 服务

### 1. Amazon Bedrock
- **用途**：访问用于 AI 功能的基础模型
- **区域可用性**：有限区域（us-east-1、us-west-2、eu-west-1 等）
- **费用**：基于输入/输出令牌的按使用付费

### 2. AWS IAM
- **用途**：服务访问的权限管理
- **费用**：免费

### 3. Amazon CloudWatch（可选）
- **用途**：日志记录和监控
- **费用**：日志存储和指标的按使用付费

### 4. Amazon S3（可选）
- **用途**：知识库或上传的文件存储
- **费用**：存储和请求的按使用付费

## 🚀 分步设置

### 步骤 1：启用 Amazon Bedrock 访问

1. **导航到 Amazon Bedrock 控制台**
   ```
   https://console.aws.amazon.com/bedrock/
   ```

2. **选择您的区域**
   - 选择 Bedrock 可用的区域
   - 推荐区域：`us-east-1`、`us-west-2`、`eu-west-1`

3. **请求模型访问**
   - 在左侧边栏中转到"模型访问"
   - 点击"请求模型访问"
   - 选择您想要使用的模型：
     - ✅ **Anthropic Claude 3 Sonnet**（推荐）
     - ✅ **Anthropic Claude 3 Haiku**（经济实惠）
     - ✅ **Amazon Titan Text Express**
     - ✅ **Meta Llama 2 13B**（可选）

4. **提交访问请求**
   - 填写用例表单
   - 大多数请求会立即获得批准
   - 某些模型可能需要人工审核

### 步骤 2：创建 IAM 策略

1. **导航到 IAM 控制台**
   ```
   https://console.aws.amazon.com/iam/
   ```

2. **创建自定义策略**
   - 转到"策略" → "创建策略"
   - 选择"JSON"选项卡
   - 粘贴以下策略：

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "BedrockModelAccess",
      "Effect": "Allow",
      "Action": [
        "bedrock:InvokeModel",
        "bedrock:InvokeModelWithResponseStream",
        "bedrock:GetFoundationModel",
        "bedrock:ListFoundationModels"
      ],
      "Resource": [
        "arn:aws:bedrock:*::foundation-model/anthropic.claude-3-sonnet-20240229-v1:0",
        "arn:aws:bedrock:*::foundation-model/anthropic.claude-3-haiku-20240307-v1:0",
        "arn:aws:bedrock:*::foundation-model/amazon.titan-text-express-v1",
        "arn:aws:bedrock:*::foundation-model/meta.llama2-13b-chat-v1"
      ]
    },
    {
      "Sid": "CloudWatchLogs",
      "Effect": "Allow",
      "Action": [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents",
        "logs:DescribeLogGroups",
        "logs:DescribeLogStreams"
      ],
      "Resource": "arn:aws:logs:*:*:log-group:/aws/bedrock-agent/*"
    },
    {
      "Sid": "S3Access",
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::your-bedrock-agent-bucket",
        "arn:aws:s3:::your-bedrock-agent-bucket/*"
      ]
    }
  ]
}
```

3. **命名策略**
   - 策略名称：`BedrockAgentPolicy`
   - 描述：`Bedrock Agent 应用程序的策略`

### 步骤 3：创建 IAM 用户或角色

#### 选项 A：IAM 用户（开发环境）

1. **创建 IAM 用户**
   - 转到"用户" → "创建用户"
   - 用户名：`bedrock-agent-user`
   - 访问类型："编程访问"

2. **附加策略**
   - 附加上面创建的 `BedrockAgentPolicy`
   - 完成用户创建

3. **保存凭证**
   - 下载包含访问密钥的 CSV 文件
   - 安全存储（永远不要提交到代码中）

#### 选项 B：IAM 角色（生产环境）

1. **创建 IAM 角色**
   - 转到"角色" → "创建角色"
   - 受信任实体："AWS 服务"
   - 服务："EC2"（或相关服务）

2. **附加策略**
   - 附加 `BedrockAgentPolicy`
   - 角色名称：`BedrockAgentRole`

### 步骤 4：配置环境变量

创建您的 `.env` 文件，包含以下配置：

```env
# AWS 配置
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=AKIA...  # 来自 IAM 用户
AWS_SECRET_ACCESS_KEY=...  # 来自 IAM 用户

# Bedrock 配置
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0

# 可选：CloudWatch 配置
CLOUDWATCH_LOG_GROUP=/aws/bedrock-agent
CLOUDWATCH_LOG_STREAM=bedrock-agent-stream

# 可选：S3 配置
S3_BUCKET_NAME=your-bedrock-agent-bucket
```

### 步骤 5：测试 AWS 连接

1. **安装 AWS CLI**（如果尚未安装）
   ```bash
   # macOS
   brew install awscli
   
   # Windows
   # 从 https://aws.amazon.com/cli/ 下载
   
   # Linux
   sudo apt-get install awscli
   ```

2. **配置 AWS CLI**
   ```bash
   aws configure
   # 输入您的访问密钥、秘密密钥、区域和输出格式
   ```

3. **测试 Bedrock 访问**
   ```bash
   # 列出可用模型
   aws bedrock list-foundation-models --region us-east-1
   
   # 测试模型调用
   aws bedrock-runtime invoke-model \
     --model-id anthropic.claude-3-sonnet-20240229-v1:0 \
     --body '{"prompt":"Human: 你好\n\nAssistant:","max_tokens_to_sample":100}' \
     --region us-east-1 \
     output.json
   ```

## 🔒 安全最佳实践

### 1. 凭证管理

- **永远不要硬编码凭证**到您的应用程序中
- 在 AWS 服务上运行时使用 **AWS IAM 角色**
- 在生产环境中使用 **AWS Secrets Manager** 存储机密
- **定期轮换访问密钥**

### 2. 网络安全

- 使用 **VPC 端点**进行私有通信
- 配置**安全组**以限制访问
- 启用 **AWS CloudTrail** 进行 API 审计

### 3. 访问控制

- 遵循**最小权限原则**
- 尽可能使用**基于资源的策略**
- 为敏感操作启用 **MFA**
- 定期进行**访问审查**

## 💰 成本优化

### 1. 模型选择

| 模型 | 输入成本（每 1K 令牌） | 输出成本（每 1K 令牌） | 用例 |
|-------|----------------------------|------------------------------|----------|
| Claude 3 Haiku | $0.00025 | $0.00125 | 简单任务，高容量 |
| Claude 3 Sonnet | $0.003 | $0.015 | 平衡性能 |
| Claude 3 Opus | $0.015 | $0.075 | 复杂推理 |
| Titan Text Express | $0.0008 | $0.0016 | 经济实惠 |

### 2. 使用优化

- **实施缓存**以处理重复查询
- **使用会话管理**来维护上下文
- **设置适当的令牌限制**
- **使用 CloudWatch 监控使用情况**

### 3. 成本监控

```bash
# 设置计费警报
aws budgets create-budget \
  --account-id ************ \
  --budget file://budget.json
```

## 🚨 故障排除

### 常见问题

1. **模型访问被拒绝**
   ```
   错误：AccessDeniedException
   解决方案：在 Bedrock 控制台中请求模型访问
   ```

2. **无效凭证**
   ```
   错误：InvalidSignatureException
   解决方案：检查 AWS 凭证和区域
   ```

3. **速率限制**
   ```
   错误：ThrottlingException
   解决方案：实施指数退避
   ```

4. **不支持的区域**
   ```
   错误：ValidationException
   解决方案：使用支持的区域（us-east-1、us-west-2 等）
   ```

### 调试命令

```bash
# 检查 AWS 凭证
aws sts get-caller-identity

# 列出 Bedrock 模型
aws bedrock list-foundation-models

# 检查 CloudWatch 日志
aws logs describe-log-groups --log-group-name-prefix "/aws/bedrock"
```

## 📞 支持资源

- **AWS Bedrock 文档**：https://docs.aws.amazon.com/bedrock/
- **AWS 支持**：https://aws.amazon.com/support/
- **AWS 论坛**：https://forums.aws.amazon.com/
- **AWS CLI 参考**：https://docs.aws.amazon.com/cli/

## 🔄 后续步骤

完成 AWS 设置后：

1. ✅ 在本地测试应用程序
2. ✅ 部署到您首选的环境
3. ✅ 设置监控和警报
4. ✅ 配置备份和灾难恢复
5. ✅ 实施安全扫描和合规性

/**
 * AWS Bedrock Agent 应用程序
 * Node.js 应用程序的主入口点
 *
 * 这个文件负责：
 * 1. 初始化 Express 服务器
 * 2. 配置中间件和路由
 * 3. 启动应用程序
 * 4. 处理优雅关闭
 */

import express from 'express'
import { config } from './config/index.js'
import { initializeAWS } from './config/aws.js'
import { applyMiddleware, errorHandlingMiddleware, notFoundMiddleware } from './middleware/index.js'
import routes from './routes/index.js'
import { logger, logStartup, logShutdown } from './utils/logger.js'

/**
 * 创建 Express 应用程序实例
 * 这是整个 Web 服务器的核心对象
 */
const app = express()

/**
 * 初始化应用程序
 *
 * 这个函数按顺序执行以下步骤：
 * 1. 记录启动信息
 * 2. 初始化 AWS 服务连接
 * 3. 应用中间件（安全、日志、解析等）
 * 4. 设置 API 路由
 * 5. 配置错误处理
 *
 * @returns {Promise<Express>} 配置完成的 Express 应用程序
 */
async function initializeApp() {
  try {
    // 记录应用程序启动信息
    logStartup(config)

    // 初始化 AWS 服务（Bedrock 客户端等）
    // 这会验证 AWS 凭证并建立连接
    await initializeAWS()
    logger.info('AWS 服务初始化成功')

    // 应用中间件
    // 包括安全头、CORS、速率限制、请求解析等
    applyMiddleware(app)

    // 应用路由
    // 设置所有 API 端点（/api/v1/chat、/health 等）
    app.use('/', routes)

    // 应用错误处理中间件（必须在最后）
    // 处理 404 错误和其他未捕获的错误
    app.use(notFoundMiddleware())
    app.use(errorHandlingMiddleware())

    logger.info('应用程序初始化成功')
    return app
  } catch (error) {
    logger.error('应用程序初始化失败:', error)
    throw error
  }
}

/**
 * Start the server
 */
async function startServer() {
  try {
    const app = await initializeApp()

    const server = app.listen(config.server.port, config.server.host, () => {
      logger.info(`Server started successfully`, {
        port: config.server.port,
        host: config.server.host,
        environment: config.server.nodeEnv,
        pid: process.pid
      })
    })

    // Graceful shutdown handling
    const gracefulShutdown = (signal) => {
      logger.info(`Received ${signal}, starting graceful shutdown`)

      server.close((err) => {
        if (err) {
          logger.error('Error during server shutdown:', err)
          process.exit(1)
        }

        logShutdown()
        process.exit(0)
      })

      // Force shutdown after 30 seconds
      setTimeout(() => {
        logger.error('Forced shutdown after timeout')
        process.exit(1)
      }, 30000)
    }

    // Handle shutdown signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'))
    process.on('SIGINT', () => gracefulShutdown('SIGINT'))

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception:', error)
      gracefulShutdown('uncaughtException')
    })

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled promise rejection:', { reason, promise })
      gracefulShutdown('unhandledRejection')
    })

    return server
  } catch (error) {
    logger.error('Failed to start server:', error)
    process.exit(1)
  }
}

// Start the application if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  startServer()
}

export { app, startServer, initializeApp }
export default app
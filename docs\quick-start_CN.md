# 快速开始指南

这个指南将帮助您在 10 分钟内启动并运行 AWS Bedrock Agent 应用程序。

## 🎯 目标

完成本指南后，您将拥有一个运行中的 AI 聊天应用程序，可以：
- 与 AI 助手进行对话
- 管理多个会话
- 处理不同类型的请求
- 监控应用程序状态

## ⏱️ 预计时间：10-15 分钟

## 📋 准备工作

在开始之前，请确保您有：

### 1. 系统要求
- **Node.js 18+**：[下载地址](https://nodejs.org/)
- **npm 8+**：通常与 Node.js 一起安装
- **Git**：[下载地址](https://git-scm.com/)

### 2. AWS 账户
- 有效的 AWS 账户
- 访问 Amazon Bedrock 服务的权限
- IAM 用户的访问密钥和秘密密钥

### 3. 检查系统
```bash
# 检查 Node.js 版本
node --version
# 应该显示 v18.0.0 或更高版本

# 检查 npm 版本
npm --version
# 应该显示 8.0.0 或更高版本
```

## 🚀 第一步：获取代码

### 1. 克隆项目
```bash
git clone https://github.com/your-username/aws-bedrock-agent.git
cd aws-bedrock-agent
```

### 2. 安装依赖
```bash
npm install
```

这将安装所有必需的依赖包，可能需要 2-3 分钟。

## ⚙️ 第二步：配置 AWS

### 1. 创建环境配置文件
```bash
# 复制示例配置文件
cp .env.example .env
```

### 2. 编辑配置文件
打开 `.env` 文件并填入您的 AWS 信息：

```env
# 必需配置 - 请替换为您的实际值
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=您的访问密钥ID
AWS_SECRET_ACCESS_KEY=您的秘密访问密钥

# 选择 AI 模型（推荐使用 Claude 3 Sonnet）
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0

# 服务器配置（可选修改）
PORT=3000
NODE_ENV=development
```

### 3. 获取 AWS 凭证

如果您还没有 AWS 访问密钥：

1. 登录 [AWS 控制台](https://console.aws.amazon.com/)
2. 转到 IAM 服务
3. 创建新用户或使用现有用户
4. 附加 Bedrock 权限策略
5. 生成访问密钥

**详细步骤请参考：** [AWS 设置指南](aws-setup_CN.md)

## 🔑 第三步：设置 Bedrock 权限

### 1. 请求模型访问
1. 打开 [Amazon Bedrock 控制台](https://console.aws.amazon.com/bedrock/)
2. 在左侧菜单选择"模型访问"
3. 点击"请求模型访问"
4. 选择以下模型：
   - ✅ Anthropic Claude 3 Sonnet
   - ✅ Anthropic Claude 3 Haiku
   - ✅ Amazon Titan Text Express

### 2. 等待批准
- 大多数模型会立即获得批准
- 某些模型可能需要几分钟到几小时

### 3. 验证访问
```bash
# 测试 AWS 连接（需要安装 AWS CLI）
aws bedrock list-foundation-models --region us-east-1
```

## 🏃‍♂️ 第四步：启动应用程序

### 1. 启动开发服务器
```bash
npm run dev
```

您应该看到类似的输出：
```
🚀 AWS Bedrock Agent API 示例
====================================
✅ AWS 服务初始化成功
✅ 应用程序初始化成功
🌐 服务器启动成功
   端口: 3000
   环境: development
```

### 2. 验证应用程序运行
打开新的终端窗口并运行：
```bash
curl http://localhost:3000/health
```

您应该看到：
```json
{
  "status": "healthy",
  "timestamp": "2024-08-26T10:30:00.000Z",
  "uptime": 5.123,
  "version": "1.0.0"
}
```

## 🧪 第五步：测试 API

### 1. 发送第一条消息
```bash
curl -X POST http://localhost:3000/api/v1/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "你好！你能帮助我吗？",
    "userId": "test-user"
  }'
```

### 2. 预期响应
```json
{
  "success": true,
  "data": {
    "sessionId": "550e8400-e29b-41d4-a716-446655440000",
    "message": "你好！我是由 Amazon Bedrock 驱动的 AI 助手。我很乐意帮助您！请告诉我您需要什么帮助。",
    "metadata": {
      "modelId": "anthropic.claude-3-sonnet-20240229-v1:0",
      "tokensUsed": 45,
      "processingTime": 1250
    }
  }
}
```

### 3. 继续对话
使用返回的 `sessionId` 继续对话：
```bash
curl -X POST http://localhost:3000/api/v1/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "解释一下什么是人工智能",
    "sessionId": "550e8400-e29b-41d4-a716-446655440000",
    "userId": "test-user"
  }'
```

## 🎉 第六步：运行完整示例

### 1. 运行内置示例
```bash
node examples/api-examples_CN.js
```

这将运行一系列示例，展示：
- 基本聊天功能
- 会话管理
- 自定义参数
- 错误处理
- 健康检查

### 2. 查看示例输出
您将看到详细的交互示例和 AI 响应。

## 📊 第七步：监控和管理

### 1. 检查应用程序健康
```bash
# 基本健康检查
curl http://localhost:3000/health

# 详细健康检查
curl http://localhost:3000/api/v1/health

# 应用程序指标
curl http://localhost:3000/api/v1/metrics
```

### 2. 查看日志
```bash
# 查看应用程序日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log
```

### 3. 会话管理
```bash
# 获取用户的所有会话
curl http://localhost:3000/api/v1/chat/user/test-user/sessions

# 获取特定会话信息
curl http://localhost:3000/api/v1/chat/session/您的会话ID

# 清除会话
curl -X DELETE http://localhost:3000/api/v1/chat/session/您的会话ID
```

## 🎯 下一步

现在您的应用程序已经运行，您可以：

### 1. 自定义配置
- 尝试不同的 AI 模型
- 调整温度和令牌限制
- 配置自定义系统提示

### 2. 集成到您的应用
- 使用 REST API 构建前端界面
- 集成到现有的应用程序中
- 添加用户认证和授权

### 3. 部署到生产环境
- 参考 [部署指南](deployment_CN.md)
- 设置监控和警报
- 配置负载均衡和自动扩展

### 4. 高级功能
- 添加知识库集成
- 实现文件上传功能
- 配置流式响应

## 🚨 常见问题

### 问题：应用程序无法启动
**解决方案：**
1. 检查 Node.js 版本是否 >= 18
2. 确认 `.env` 文件存在且配置正确
3. 验证 AWS 凭证有效

### 问题：API 返回认证错误
**解决方案：**
1. 检查 AWS 访问密钥是否正确
2. 确认 IAM 用户有 Bedrock 权限
3. 验证 AWS 区域设置

### 问题：模型访问被拒绝
**解决方案：**
1. 在 Bedrock 控制台请求模型访问
2. 等待访问请求批准
3. 检查模型 ID 是否正确

### 问题：端口被占用
**解决方案：**
```bash
# 更改端口（在 .env 文件中）
PORT=3001

# 或查找并停止占用端口的进程
lsof -i :3000
kill -9 进程ID
```

## 📚 更多资源

- **完整文档**：[README_CN.md](../README_CN.md)
- **AWS 设置**：[aws-setup_CN.md](aws-setup_CN.md)
- **部署指南**：[deployment_CN.md](deployment_CN.md)
- **故障排除**：[troubleshooting_CN.md](troubleshooting_CN.md)
- **API 示例**：[examples/api-examples_CN.js](../examples/api-examples_CN.js)

## 🎊 恭喜！

您已经成功设置并运行了 AWS Bedrock Agent 应用程序！现在您可以开始构建自己的 AI 驱动的应用程序了。

如果您遇到任何问题，请查看故障排除指南或在 GitHub 上创建 issue。

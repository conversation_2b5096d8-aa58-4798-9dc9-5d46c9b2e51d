/**
 * 请求验证工具
 * 基于 Joi 的 API 请求验证模式
 *
 * 这个文件负责：
 * 1. 验证用户输入的数据格式和内容
 * 2. 确保 API 请求的安全性
 * 3. 提供清晰的错误消息
 * 4. 防止恶意输入和数据注入
 */

import Joi from 'joi'
import { FOUNDATION_MODELS } from '../config/aws.js'

/**
 * 聊天请求验证模式
 *
 * 这个模式定义了聊天 API 接受的数据格式和限制
 * 包括消息内容、会话 ID、用户 ID 等字段的验证规则
 */
const chatRequestSchema = Joi.object({
  // 消息内容验证 - 这是用户发送给 AI 的文本
  message: Joi.string()
    .min(1)                    // 最少 1 个字符
    .max(10000)               // 最多 10,000 个字符（防止过长消息）
    .required()               // 必填字段
    .messages({
      'string.empty': '消息不能为空',
      'string.min': '消息至少需要 1 个字符',
      'string.max': '消息不能超过 10,000 个字符',
      'any.required': '消息是必需的'
    }),

  sessionId: Joi.string()
    .uuid()
    .optional()
    .messages({
      'string.guid': 'Session ID must be a valid UUID'
    }),

  userId: Joi.string()
    .min(1)
    .max(100)
    .optional()
    .messages({
      'string.min': 'User ID must be at least 1 character long',
      'string.max': 'User ID cannot exceed 100 characters'
    }),

  streaming: Joi.boolean()
    .optional()
    .default(false),

  systemPrompt: Joi.string()
    .max(5000)
    .optional()
    .messages({
      'string.max': 'System prompt cannot exceed 5,000 characters'
    }),

  temperature: Joi.number()
    .min(0)
    .max(1)
    .optional()
    .messages({
      'number.min': 'Temperature must be between 0 and 1',
      'number.max': 'Temperature must be between 0 and 1'
    }),

  maxTokens: Joi.number()
    .integer()
    .min(1)
    .max(8192)
    .optional()
    .messages({
      'number.integer': 'Max tokens must be an integer',
      'number.min': 'Max tokens must be at least 1',
      'number.max': 'Max tokens cannot exceed 8,192'
    }),

  modelId: Joi.string()
    .valid(...Object.values(FOUNDATION_MODELS))
    .optional()
    .messages({
      'any.only': 'Invalid model ID. Must be one of the supported foundation models'
    })
})

/**
 * Session ID validation schema
 */
const sessionIdSchema = Joi.string()
  .uuid()
  .required()
  .messages({
    'string.guid': 'Session ID must be a valid UUID',
    'any.required': 'Session ID is required'
  })

/**
 * User ID validation schema
 */
const userIdSchema = Joi.string()
  .min(1)
  .max(100)
  .required()
  .messages({
    'string.min': 'User ID must be at least 1 character long',
    'string.max': 'User ID cannot exceed 100 characters',
    'any.required': 'User ID is required'
  })

/**
 * Health check request schema
 */
const healthCheckSchema = Joi.object({
  detailed: Joi.boolean()
    .optional()
    .default(false)
})

/**
 * Configuration update schema
 */
const configUpdateSchema = Joi.object({
  modelId: Joi.string()
    .valid(...Object.values(FOUNDATION_MODELS))
    .optional(),

  temperature: Joi.number()
    .min(0)
    .max(1)
    .optional(),

  maxTokens: Joi.number()
    .integer()
    .min(1)
    .max(8192)
    .optional(),

  systemPrompt: Joi.string()
    .max(5000)
    .optional()
}).min(1).messages({
  'object.min': 'At least one configuration parameter must be provided'
})

/**
 * Validate chat request
 * @param {object} data - Request data to validate
 * @returns {object} Validation result
 */
export function validateChatRequest(data) {
  return chatRequestSchema.validate(data, {
    abortEarly: false,
    stripUnknown: true
  })
}

/**
 * Validate session ID
 * @param {string} sessionId - Session ID to validate
 * @returns {object} Validation result
 */
export function validateSessionId(sessionId) {
  return sessionIdSchema.validate(sessionId)
}

/**
 * Validate user ID
 * @param {string} userId - User ID to validate
 * @returns {object} Validation result
 */
export function validateUserId(userId) {
  return userIdSchema.validate(userId)
}

/**
 * Validate health check request
 * @param {object} data - Request data to validate
 * @returns {object} Validation result
 */
export function validateHealthCheck(data) {
  return healthCheckSchema.validate(data, {
    stripUnknown: true
  })
}

/**
 * Validate configuration update
 * @param {object} data - Configuration data to validate
 * @returns {object} Validation result
 */
export function validateConfigUpdate(data) {
  return configUpdateSchema.validate(data, {
    abortEarly: false,
    stripUnknown: true
  })
}

/**
 * Custom validation middleware
 * @param {object} schema - Joi validation schema
 * @param {string} source - Source of data to validate ('body', 'params', 'query')
 * @returns {function} Express middleware function
 */
export function validateRequest(schema, source = 'body') {
  return (req, res, next) => {
    const data = req[source]
    const { error, value } = schema.validate(data, {
      abortEarly: false,
      stripUnknown: true
    })

    if (error) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value
        }))
      })
    }

    // Replace the original data with validated and sanitized data
    req[source] = value
    next()
  }
}

/**
 * Sanitize string input
 * @param {string} input - Input string to sanitize
 * @returns {string} Sanitized string
 */
export function sanitizeString(input) {
  if (typeof input !== 'string') {
    return input
  }

  return input
    .trim()
    .replace(/[\x00-\x1F\x7F]/g, '') // Remove control characters
    .replace(/\s+/g, ' ') // Normalize whitespace
}

/**
 * Validate and sanitize message content
 * @param {string} message - Message content
 * @returns {object} Validation result with sanitized content
 */
export function validateAndSanitizeMessage(message) {
  // First sanitize
  const sanitized = sanitizeString(message)
  
  // Then validate
  const schema = Joi.string().min(1).max(10000).required()
  const { error, value } = schema.validate(sanitized)
  
  return {
    error,
    value,
    sanitized: value
  }
}

/**
 * Rate limiting validation
 * @param {object} req - Express request object
 * @returns {boolean} Whether request should be rate limited
 */
export function shouldRateLimit(req) {
  // Skip rate limiting for health checks
  if (req.path === '/health' || req.path === '/api/v1/health') {
    return false
  }

  // Skip rate limiting for internal requests
  if (req.headers['x-internal-request'] === 'true') {
    return false
  }

  return true
}

/**
 * Validate file upload
 * @param {object} file - Uploaded file object
 * @returns {object} Validation result
 */
export function validateFileUpload(file) {
  const schema = Joi.object({
    fieldname: Joi.string().required(),
    originalname: Joi.string().required(),
    encoding: Joi.string().required(),
    mimetype: Joi.string().valid(
      'text/plain',
      'text/markdown',
      'application/pdf',
      'application/json'
    ).required(),
    size: Joi.number().max(10 * 1024 * 1024).required() // 10MB max
  })

  return schema.validate(file)
}

/**
 * Common validation patterns
 */
export const patterns = {
  uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  slug: /^[a-z0-9]+(?:-[a-z0-9]+)*$/
}

/**
 * Validation error formatter
 * @param {object} error - Joi validation error
 * @returns {object} Formatted error response
 */
export function formatValidationError(error) {
  return {
    success: false,
    error: 'Validation failed',
    details: error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value,
      type: detail.type
    })),
    timestamp: new Date().toISOString()
  }
}

export default {
  validateChatRequest,
  validateSessionId,
  validateUserId,
  validateHealthCheck,
  validateConfigUpdate,
  validateRequest,
  sanitizeString,
  validateAndSanitizeMessage,
  shouldRateLimit,
  validateFileUpload,
  patterns,
  formatValidationError
}

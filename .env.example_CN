# AWS Bedrock Agent 环境配置文件
# 这个文件包含了应用程序运行所需的所有配置变量
# 请复制此文件为 .env 并填入您的实际配置值

# ==========================================
# AWS 配置 - 必需设置
# ==========================================

# AWS 区域 - 选择支持 Bedrock 的区域
# 推荐区域：us-east-1（美国东部）、us-west-2（美国西部）、eu-west-1（欧洲西部）
AWS_REGION=us-east-1

# AWS 访问密钥 ID - 从 IAM 用户获取
# 这是您的 AWS 账户访问凭证的一部分
AWS_ACCESS_KEY_ID=your_access_key_here

# AWS 秘密访问密钥 - 从 IAM 用户获取
# 这是您的 AWS 账户访问凭证的另一部分，请妥善保管
AWS_SECRET_ACCESS_KEY=your_secret_key_here

# AWS 会话令牌 - 仅在使用临时凭证时需要
# 如果您使用 IAM 角色或临时凭证，请填写此项
AWS_SESSION_TOKEN=your_session_token_here_if_using_temporary_credentials

# ==========================================
# Amazon Bedrock 配置
# ==========================================

# Bedrock 模型 ID - 选择要使用的 AI 模型
# 推荐选项：
# - anthropic.claude-3-sonnet-20240229-v1:0 （平衡性能和成本）
# - anthropic.claude-3-haiku-20240307-v1:0 （快速且经济）
# - amazon.titan-text-express-v1 （AWS 原生模型）
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0

# Bedrock 代理 ID - 如果使用预配置的代理，请填写
# 大多数情况下可以留空，使用基础模型
BEDROCK_AGENT_ID=your_agent_id_here

# Bedrock 代理别名 ID - 代理的版本标识
# 默认值通常是 TSTALIASID（测试别名）
BEDROCK_AGENT_ALIAS_ID=TSTALIASID

# 会话生存时间（秒）- 会话在多长时间后过期
# 3600 秒 = 1 小时，可根据需要调整
BEDROCK_SESSION_TTL=3600

# ==========================================
# 服务器配置
# ==========================================

# 服务器端口 - 应用程序监听的端口号
# 默认 3000，如果端口被占用可以更改
PORT=3000

# Node.js 运行环境
# development（开发）、production（生产）、test（测试）
NODE_ENV=development

# API 版本号 - 用于 URL 路径
API_VERSION=v1

# ==========================================
# 安全配置
# ==========================================

# JWT 密钥 - 用于生成和验证 JSON Web Tokens
# 在生产环境中请使用强密码
JWT_SECRET=your_jwt_secret_here

# 速率限制时间窗口（毫秒）
# 900000 毫秒 = 15 分钟
API_RATE_LIMIT_WINDOW_MS=900000

# 速率限制最大请求数
# 在时间窗口内允许的最大请求数
API_RATE_LIMIT_MAX_REQUESTS=100

# ==========================================
# 日志配置
# ==========================================

# 日志级别 - 控制日志的详细程度
# 选项：error（错误）、warn（警告）、info（信息）、debug（调试）
LOG_LEVEL=info

# 日志文件路径 - 日志文件的存储位置
LOG_FILE_PATH=logs/app.log

# ==========================================
# CORS 配置（跨域资源共享）
# ==========================================

# 允许的源域名 - 哪些网站可以访问您的 API
# 多个域名用逗号分隔
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# 是否允许发送凭证（cookies、认证头等）
CORS_CREDENTIALS=true

# ==========================================
# 健康检查配置
# ==========================================

# 健康检查间隔（毫秒）
# 30000 毫秒 = 30 秒
HEALTH_CHECK_INTERVAL=30000

# ==========================================
# 会话管理配置
# ==========================================

# 会话存储类型
# memory（内存）- 适合开发和小规模部署
# redis（Redis）- 适合生产环境和集群部署
SESSION_STORE_TYPE=memory

# 会话清理间隔（毫秒）
# 300000 毫秒 = 5 分钟
SESSION_CLEANUP_INTERVAL=300000

# ==========================================
# 可选：知识库配置
# ==========================================

# 知识库 ID - 如果使用 Bedrock 知识库功能
# 可以为空，不使用知识库
KNOWLEDGE_BASE_ID=your_knowledge_base_id_here

# 知识库检索配置类型
# VECTOR（向量检索）是最常用的类型
KNOWLEDGE_BASE_RETRIEVAL_CONFIG_TYPE=VECTOR

# ==========================================
# 可选：S3 配置（文件上传）
# ==========================================

# S3 存储桶名称 - 用于存储上传的文件
# 如果不需要文件上传功能可以留空
S3_BUCKET_NAME=your_s3_bucket_name

# S3 区域 - 存储桶所在的 AWS 区域
# 通常与 AWS_REGION 相同
S3_REGION=us-east-1

# ==========================================
# 可选：CloudWatch 配置（监控和日志）
# ==========================================

# CloudWatch 日志组名称
CLOUDWATCH_LOG_GROUP=/aws/lambda/bedrock-agent

# CloudWatch 日志流名称
CLOUDWATCH_LOG_STREAM=bedrock-agent-stream

# ==========================================
# 配置说明
# ==========================================

# 1. 必需配置：AWS_REGION、AWS_ACCESS_KEY_ID、AWS_SECRET_ACCESS_KEY
# 2. 推荐配置：BEDROCK_MODEL_ID（选择合适的 AI 模型）
# 3. 安全提醒：
#    - 永远不要将 .env 文件提交到版本控制系统
#    - 在生产环境中使用强密码和安全的密钥
#    - 定期轮换 AWS 访问密钥
# 4. 性能调优：
#    - 根据预期负载调整速率限制
#    - 根据内存使用情况调整会话清理间隔
#    - 选择最适合您用例的 AI 模型

# 如需帮助，请参考：
# - README_CN.md：完整的设置指南
# - docs/aws-setup_CN.md：AWS 配置详细说明

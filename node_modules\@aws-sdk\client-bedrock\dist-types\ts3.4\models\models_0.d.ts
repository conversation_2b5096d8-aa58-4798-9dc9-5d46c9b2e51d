import { ExceptionOptionType as __ExceptionOptionType } from "@smithy/smithy-client";
import { DocumentType as __DocumentType } from "@smithy/types";
import { BedrockServiceException as __BaseException } from "./BedrockServiceException";
export declare class AccessDeniedException extends __BaseException {
  readonly name: "AccessDeniedException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<AccessDeniedException, __BaseException>
  );
}
export declare const AgreementStatus: {
  readonly AVAILABLE: "AVAILABLE";
  readonly ERROR: "ERROR";
  readonly NOT_AVAILABLE: "NOT_AVAILABLE";
  readonly PENDING: "PENDING";
};
export type AgreementStatus =
  (typeof AgreementStatus)[keyof typeof AgreementStatus];
export interface AgreementAvailability {
  status: AgreementStatus | undefined;
  errorMessage?: string | undefined;
}
export interface GetUseCaseForModelAccessRequest {}
export interface GetUseCaseForModelAccessResponse {
  formData: Uint8Array | undefined;
}
export declare class InternalServerException extends __BaseException {
  readonly name: "InternalServerException";
  readonly $fault: "server";
  constructor(
    opts: __ExceptionOptionType<InternalServerException, __BaseException>
  );
}
export declare class ResourceNotFoundException extends __BaseException {
  readonly name: "ResourceNotFoundException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<ResourceNotFoundException, __BaseException>
  );
}
export declare class ThrottlingException extends __BaseException {
  readonly name: "ThrottlingException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<ThrottlingException, __BaseException>
  );
}
export declare class ValidationException extends __BaseException {
  readonly name: "ValidationException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<ValidationException, __BaseException>
  );
}
export interface PutUseCaseForModelAccessRequest {
  formData: Uint8Array | undefined;
}
export interface PutUseCaseForModelAccessResponse {}
export interface CancelAutomatedReasoningPolicyBuildWorkflowRequest {
  policyArn: string | undefined;
  buildWorkflowId: string | undefined;
}
export interface CancelAutomatedReasoningPolicyBuildWorkflowResponse {}
export declare class ConflictException extends __BaseException {
  readonly name: "ConflictException";
  readonly $fault: "client";
  constructor(opts: __ExceptionOptionType<ConflictException, __BaseException>);
}
export interface AutomatedReasoningPolicyDefinitionRule {
  id: string | undefined;
  expression: string | undefined;
  alternateExpression?: string | undefined;
}
export interface AutomatedReasoningPolicyDefinitionTypeValue {
  value: string | undefined;
  description?: string | undefined;
}
export interface AutomatedReasoningPolicyDefinitionType {
  name: string | undefined;
  description?: string | undefined;
  values: AutomatedReasoningPolicyDefinitionTypeValue[] | undefined;
}
export interface AutomatedReasoningPolicyDefinitionVariable {
  name: string | undefined;
  type: string | undefined;
  description: string | undefined;
}
export interface AutomatedReasoningPolicyDefinition {
  version?: string | undefined;
  types?: AutomatedReasoningPolicyDefinitionType[] | undefined;
  rules?: AutomatedReasoningPolicyDefinitionRule[] | undefined;
  variables?: AutomatedReasoningPolicyDefinitionVariable[] | undefined;
}
export interface Tag {
  key: string | undefined;
  value: string | undefined;
}
export interface CreateAutomatedReasoningPolicyRequest {
  name: string | undefined;
  description?: string | undefined;
  clientRequestToken?: string | undefined;
  policyDefinition?: AutomatedReasoningPolicyDefinition | undefined;
  tags?: Tag[] | undefined;
}
export interface CreateAutomatedReasoningPolicyResponse {
  policyArn: string | undefined;
  version: string | undefined;
  name: string | undefined;
  description?: string | undefined;
  definitionHash?: string | undefined;
  createdAt: Date | undefined;
  updatedAt: Date | undefined;
}
export declare class ServiceQuotaExceededException extends __BaseException {
  readonly name: "ServiceQuotaExceededException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<ServiceQuotaExceededException, __BaseException>
  );
}
export declare class TooManyTagsException extends __BaseException {
  readonly name: "TooManyTagsException";
  readonly $fault: "client";
  resourceName?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<TooManyTagsException, __BaseException>
  );
}
export declare const AutomatedReasoningCheckResult: {
  readonly IMPOSSIBLE: "IMPOSSIBLE";
  readonly INVALID: "INVALID";
  readonly NO_TRANSLATION: "NO_TRANSLATION";
  readonly SATISFIABLE: "SATISFIABLE";
  readonly TOO_COMPLEX: "TOO_COMPLEX";
  readonly TRANSLATION_AMBIGUOUS: "TRANSLATION_AMBIGUOUS";
  readonly VALID: "VALID";
};
export type AutomatedReasoningCheckResult =
  (typeof AutomatedReasoningCheckResult)[keyof typeof AutomatedReasoningCheckResult];
export interface CreateAutomatedReasoningPolicyTestCaseRequest {
  policyArn: string | undefined;
  guardContent: string | undefined;
  queryContent?: string | undefined;
  expectedAggregatedFindingsResult: AutomatedReasoningCheckResult | undefined;
  clientRequestToken?: string | undefined;
  confidenceThreshold?: number | undefined;
}
export interface CreateAutomatedReasoningPolicyTestCaseResponse {
  policyArn: string | undefined;
  testCaseId: string | undefined;
}
export interface CreateAutomatedReasoningPolicyVersionRequest {
  policyArn: string | undefined;
  clientRequestToken?: string | undefined;
  lastUpdatedDefinitionHash: string | undefined;
  tags?: Tag[] | undefined;
}
export interface CreateAutomatedReasoningPolicyVersionResponse {
  policyArn: string | undefined;
  version: string | undefined;
  name: string | undefined;
  description?: string | undefined;
  definitionHash: string | undefined;
  createdAt: Date | undefined;
}
export interface DeleteAutomatedReasoningPolicyRequest {
  policyArn: string | undefined;
}
export interface DeleteAutomatedReasoningPolicyResponse {}
export interface DeleteAutomatedReasoningPolicyBuildWorkflowRequest {
  policyArn: string | undefined;
  buildWorkflowId: string | undefined;
  lastUpdatedAt: Date | undefined;
}
export interface DeleteAutomatedReasoningPolicyBuildWorkflowResponse {}
export interface DeleteAutomatedReasoningPolicyTestCaseRequest {
  policyArn: string | undefined;
  testCaseId: string | undefined;
  lastUpdatedAt: Date | undefined;
}
export interface DeleteAutomatedReasoningPolicyTestCaseResponse {}
export declare class ResourceInUseException extends __BaseException {
  readonly name: "ResourceInUseException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<ResourceInUseException, __BaseException>
  );
}
export interface ExportAutomatedReasoningPolicyVersionRequest {
  policyArn: string | undefined;
}
export interface ExportAutomatedReasoningPolicyVersionResponse {
  policyDefinition: AutomatedReasoningPolicyDefinition | undefined;
}
export interface GetAutomatedReasoningPolicyRequest {
  policyArn: string | undefined;
}
export interface GetAutomatedReasoningPolicyResponse {
  policyArn: string | undefined;
  name: string | undefined;
  version: string | undefined;
  policyId: string | undefined;
  description?: string | undefined;
  definitionHash: string | undefined;
  createdAt?: Date | undefined;
  updatedAt: Date | undefined;
}
export interface GetAutomatedReasoningPolicyAnnotationsRequest {
  policyArn: string | undefined;
  buildWorkflowId: string | undefined;
}
export interface AutomatedReasoningPolicyAddRuleAnnotation {
  expression: string | undefined;
}
export interface AutomatedReasoningPolicyAddRuleFromNaturalLanguageAnnotation {
  naturalLanguage: string | undefined;
}
export interface AutomatedReasoningPolicyAddTypeAnnotation {
  name: string | undefined;
  description: string | undefined;
  values: AutomatedReasoningPolicyDefinitionTypeValue[] | undefined;
}
export interface AutomatedReasoningPolicyAddVariableAnnotation {
  name: string | undefined;
  type: string | undefined;
  description: string | undefined;
}
export interface AutomatedReasoningPolicyDeleteRuleAnnotation {
  ruleId: string | undefined;
}
export interface AutomatedReasoningPolicyDeleteTypeAnnotation {
  name: string | undefined;
}
export interface AutomatedReasoningPolicyDeleteVariableAnnotation {
  name: string | undefined;
}
export interface AutomatedReasoningPolicyIngestContentAnnotation {
  content: string | undefined;
}
export interface AutomatedReasoningPolicyUpdateFromRuleFeedbackAnnotation {
  ruleIds?: string[] | undefined;
  feedback: string | undefined;
}
export interface AutomatedReasoningPolicyUpdateFromScenarioFeedbackAnnotation {
  ruleIds?: string[] | undefined;
  scenarioExpression: string | undefined;
  feedback?: string | undefined;
}
export interface AutomatedReasoningPolicyUpdateRuleAnnotation {
  ruleId: string | undefined;
  expression: string | undefined;
}
export interface AutomatedReasoningPolicyAddTypeValue {
  value: string | undefined;
  description?: string | undefined;
}
export interface AutomatedReasoningPolicyDeleteTypeValue {
  value: string | undefined;
}
export interface AutomatedReasoningPolicyUpdateTypeValue {
  value: string | undefined;
  newValue?: string | undefined;
  description?: string | undefined;
}
export type AutomatedReasoningPolicyTypeValueAnnotation =
  | AutomatedReasoningPolicyTypeValueAnnotation.AddTypeValueMember
  | AutomatedReasoningPolicyTypeValueAnnotation.DeleteTypeValueMember
  | AutomatedReasoningPolicyTypeValueAnnotation.UpdateTypeValueMember
  | AutomatedReasoningPolicyTypeValueAnnotation.$UnknownMember;
export declare namespace AutomatedReasoningPolicyTypeValueAnnotation {
  interface AddTypeValueMember {
    addTypeValue: AutomatedReasoningPolicyAddTypeValue;
    updateTypeValue?: never;
    deleteTypeValue?: never;
    $unknown?: never;
  }
  interface UpdateTypeValueMember {
    addTypeValue?: never;
    updateTypeValue: AutomatedReasoningPolicyUpdateTypeValue;
    deleteTypeValue?: never;
    $unknown?: never;
  }
  interface DeleteTypeValueMember {
    addTypeValue?: never;
    updateTypeValue?: never;
    deleteTypeValue: AutomatedReasoningPolicyDeleteTypeValue;
    $unknown?: never;
  }
  interface $UnknownMember {
    addTypeValue?: never;
    updateTypeValue?: never;
    deleteTypeValue?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    addTypeValue: (value: AutomatedReasoningPolicyAddTypeValue) => T;
    updateTypeValue: (value: AutomatedReasoningPolicyUpdateTypeValue) => T;
    deleteTypeValue: (value: AutomatedReasoningPolicyDeleteTypeValue) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(
    value: AutomatedReasoningPolicyTypeValueAnnotation,
    visitor: Visitor<T>
  ) => T;
}
export interface AutomatedReasoningPolicyUpdateTypeAnnotation {
  name: string | undefined;
  newName?: string | undefined;
  description?: string | undefined;
  values: AutomatedReasoningPolicyTypeValueAnnotation[] | undefined;
}
export interface AutomatedReasoningPolicyUpdateVariableAnnotation {
  name: string | undefined;
  newName?: string | undefined;
  description?: string | undefined;
}
export type AutomatedReasoningPolicyAnnotation =
  | AutomatedReasoningPolicyAnnotation.AddRuleMember
  | AutomatedReasoningPolicyAnnotation.AddRuleFromNaturalLanguageMember
  | AutomatedReasoningPolicyAnnotation.AddTypeMember
  | AutomatedReasoningPolicyAnnotation.AddVariableMember
  | AutomatedReasoningPolicyAnnotation.DeleteRuleMember
  | AutomatedReasoningPolicyAnnotation.DeleteTypeMember
  | AutomatedReasoningPolicyAnnotation.DeleteVariableMember
  | AutomatedReasoningPolicyAnnotation.IngestContentMember
  | AutomatedReasoningPolicyAnnotation.UpdateFromRulesFeedbackMember
  | AutomatedReasoningPolicyAnnotation.UpdateFromScenarioFeedbackMember
  | AutomatedReasoningPolicyAnnotation.UpdateRuleMember
  | AutomatedReasoningPolicyAnnotation.UpdateTypeMember
  | AutomatedReasoningPolicyAnnotation.UpdateVariableMember
  | AutomatedReasoningPolicyAnnotation.$UnknownMember;
export declare namespace AutomatedReasoningPolicyAnnotation {
  interface AddTypeMember {
    addType: AutomatedReasoningPolicyAddTypeAnnotation;
    updateType?: never;
    deleteType?: never;
    addVariable?: never;
    updateVariable?: never;
    deleteVariable?: never;
    addRule?: never;
    updateRule?: never;
    deleteRule?: never;
    addRuleFromNaturalLanguage?: never;
    updateFromRulesFeedback?: never;
    updateFromScenarioFeedback?: never;
    ingestContent?: never;
    $unknown?: never;
  }
  interface UpdateTypeMember {
    addType?: never;
    updateType: AutomatedReasoningPolicyUpdateTypeAnnotation;
    deleteType?: never;
    addVariable?: never;
    updateVariable?: never;
    deleteVariable?: never;
    addRule?: never;
    updateRule?: never;
    deleteRule?: never;
    addRuleFromNaturalLanguage?: never;
    updateFromRulesFeedback?: never;
    updateFromScenarioFeedback?: never;
    ingestContent?: never;
    $unknown?: never;
  }
  interface DeleteTypeMember {
    addType?: never;
    updateType?: never;
    deleteType: AutomatedReasoningPolicyDeleteTypeAnnotation;
    addVariable?: never;
    updateVariable?: never;
    deleteVariable?: never;
    addRule?: never;
    updateRule?: never;
    deleteRule?: never;
    addRuleFromNaturalLanguage?: never;
    updateFromRulesFeedback?: never;
    updateFromScenarioFeedback?: never;
    ingestContent?: never;
    $unknown?: never;
  }
  interface AddVariableMember {
    addType?: never;
    updateType?: never;
    deleteType?: never;
    addVariable: AutomatedReasoningPolicyAddVariableAnnotation;
    updateVariable?: never;
    deleteVariable?: never;
    addRule?: never;
    updateRule?: never;
    deleteRule?: never;
    addRuleFromNaturalLanguage?: never;
    updateFromRulesFeedback?: never;
    updateFromScenarioFeedback?: never;
    ingestContent?: never;
    $unknown?: never;
  }
  interface UpdateVariableMember {
    addType?: never;
    updateType?: never;
    deleteType?: never;
    addVariable?: never;
    updateVariable: AutomatedReasoningPolicyUpdateVariableAnnotation;
    deleteVariable?: never;
    addRule?: never;
    updateRule?: never;
    deleteRule?: never;
    addRuleFromNaturalLanguage?: never;
    updateFromRulesFeedback?: never;
    updateFromScenarioFeedback?: never;
    ingestContent?: never;
    $unknown?: never;
  }
  interface DeleteVariableMember {
    addType?: never;
    updateType?: never;
    deleteType?: never;
    addVariable?: never;
    updateVariable?: never;
    deleteVariable: AutomatedReasoningPolicyDeleteVariableAnnotation;
    addRule?: never;
    updateRule?: never;
    deleteRule?: never;
    addRuleFromNaturalLanguage?: never;
    updateFromRulesFeedback?: never;
    updateFromScenarioFeedback?: never;
    ingestContent?: never;
    $unknown?: never;
  }
  interface AddRuleMember {
    addType?: never;
    updateType?: never;
    deleteType?: never;
    addVariable?: never;
    updateVariable?: never;
    deleteVariable?: never;
    addRule: AutomatedReasoningPolicyAddRuleAnnotation;
    updateRule?: never;
    deleteRule?: never;
    addRuleFromNaturalLanguage?: never;
    updateFromRulesFeedback?: never;
    updateFromScenarioFeedback?: never;
    ingestContent?: never;
    $unknown?: never;
  }
  interface UpdateRuleMember {
    addType?: never;
    updateType?: never;
    deleteType?: never;
    addVariable?: never;
    updateVariable?: never;
    deleteVariable?: never;
    addRule?: never;
    updateRule: AutomatedReasoningPolicyUpdateRuleAnnotation;
    deleteRule?: never;
    addRuleFromNaturalLanguage?: never;
    updateFromRulesFeedback?: never;
    updateFromScenarioFeedback?: never;
    ingestContent?: never;
    $unknown?: never;
  }
  interface DeleteRuleMember {
    addType?: never;
    updateType?: never;
    deleteType?: never;
    addVariable?: never;
    updateVariable?: never;
    deleteVariable?: never;
    addRule?: never;
    updateRule?: never;
    deleteRule: AutomatedReasoningPolicyDeleteRuleAnnotation;
    addRuleFromNaturalLanguage?: never;
    updateFromRulesFeedback?: never;
    updateFromScenarioFeedback?: never;
    ingestContent?: never;
    $unknown?: never;
  }
  interface AddRuleFromNaturalLanguageMember {
    addType?: never;
    updateType?: never;
    deleteType?: never;
    addVariable?: never;
    updateVariable?: never;
    deleteVariable?: never;
    addRule?: never;
    updateRule?: never;
    deleteRule?: never;
    addRuleFromNaturalLanguage: AutomatedReasoningPolicyAddRuleFromNaturalLanguageAnnotation;
    updateFromRulesFeedback?: never;
    updateFromScenarioFeedback?: never;
    ingestContent?: never;
    $unknown?: never;
  }
  interface UpdateFromRulesFeedbackMember {
    addType?: never;
    updateType?: never;
    deleteType?: never;
    addVariable?: never;
    updateVariable?: never;
    deleteVariable?: never;
    addRule?: never;
    updateRule?: never;
    deleteRule?: never;
    addRuleFromNaturalLanguage?: never;
    updateFromRulesFeedback: AutomatedReasoningPolicyUpdateFromRuleFeedbackAnnotation;
    updateFromScenarioFeedback?: never;
    ingestContent?: never;
    $unknown?: never;
  }
  interface UpdateFromScenarioFeedbackMember {
    addType?: never;
    updateType?: never;
    deleteType?: never;
    addVariable?: never;
    updateVariable?: never;
    deleteVariable?: never;
    addRule?: never;
    updateRule?: never;
    deleteRule?: never;
    addRuleFromNaturalLanguage?: never;
    updateFromRulesFeedback?: never;
    updateFromScenarioFeedback: AutomatedReasoningPolicyUpdateFromScenarioFeedbackAnnotation;
    ingestContent?: never;
    $unknown?: never;
  }
  interface IngestContentMember {
    addType?: never;
    updateType?: never;
    deleteType?: never;
    addVariable?: never;
    updateVariable?: never;
    deleteVariable?: never;
    addRule?: never;
    updateRule?: never;
    deleteRule?: never;
    addRuleFromNaturalLanguage?: never;
    updateFromRulesFeedback?: never;
    updateFromScenarioFeedback?: never;
    ingestContent: AutomatedReasoningPolicyIngestContentAnnotation;
    $unknown?: never;
  }
  interface $UnknownMember {
    addType?: never;
    updateType?: never;
    deleteType?: never;
    addVariable?: never;
    updateVariable?: never;
    deleteVariable?: never;
    addRule?: never;
    updateRule?: never;
    deleteRule?: never;
    addRuleFromNaturalLanguage?: never;
    updateFromRulesFeedback?: never;
    updateFromScenarioFeedback?: never;
    ingestContent?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    addType: (value: AutomatedReasoningPolicyAddTypeAnnotation) => T;
    updateType: (value: AutomatedReasoningPolicyUpdateTypeAnnotation) => T;
    deleteType: (value: AutomatedReasoningPolicyDeleteTypeAnnotation) => T;
    addVariable: (value: AutomatedReasoningPolicyAddVariableAnnotation) => T;
    updateVariable: (
      value: AutomatedReasoningPolicyUpdateVariableAnnotation
    ) => T;
    deleteVariable: (
      value: AutomatedReasoningPolicyDeleteVariableAnnotation
    ) => T;
    addRule: (value: AutomatedReasoningPolicyAddRuleAnnotation) => T;
    updateRule: (value: AutomatedReasoningPolicyUpdateRuleAnnotation) => T;
    deleteRule: (value: AutomatedReasoningPolicyDeleteRuleAnnotation) => T;
    addRuleFromNaturalLanguage: (
      value: AutomatedReasoningPolicyAddRuleFromNaturalLanguageAnnotation
    ) => T;
    updateFromRulesFeedback: (
      value: AutomatedReasoningPolicyUpdateFromRuleFeedbackAnnotation
    ) => T;
    updateFromScenarioFeedback: (
      value: AutomatedReasoningPolicyUpdateFromScenarioFeedbackAnnotation
    ) => T;
    ingestContent: (
      value: AutomatedReasoningPolicyIngestContentAnnotation
    ) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(
    value: AutomatedReasoningPolicyAnnotation,
    visitor: Visitor<T>
  ) => T;
}
export interface GetAutomatedReasoningPolicyAnnotationsResponse {
  policyArn: string | undefined;
  name: string | undefined;
  buildWorkflowId: string | undefined;
  annotations: AutomatedReasoningPolicyAnnotation[] | undefined;
  annotationSetHash: string | undefined;
  updatedAt: Date | undefined;
}
export interface GetAutomatedReasoningPolicyBuildWorkflowRequest {
  policyArn: string | undefined;
  buildWorkflowId: string | undefined;
}
export declare const AutomatedReasoningPolicyBuildWorkflowType: {
  readonly IMPORT_POLICY: "IMPORT_POLICY";
  readonly INGEST_CONTENT: "INGEST_CONTENT";
  readonly REFINE_POLICY: "REFINE_POLICY";
};
export type AutomatedReasoningPolicyBuildWorkflowType =
  (typeof AutomatedReasoningPolicyBuildWorkflowType)[keyof typeof AutomatedReasoningPolicyBuildWorkflowType];
export declare const AutomatedReasoningPolicyBuildDocumentContentType: {
  readonly PDF: "pdf";
  readonly TEXT: "txt";
};
export type AutomatedReasoningPolicyBuildDocumentContentType =
  (typeof AutomatedReasoningPolicyBuildDocumentContentType)[keyof typeof AutomatedReasoningPolicyBuildDocumentContentType];
export declare const AutomatedReasoningPolicyBuildWorkflowStatus: {
  readonly BUILDING: "BUILDING";
  readonly CANCELLED: "CANCELLED";
  readonly CANCEL_REQUESTED: "CANCEL_REQUESTED";
  readonly COMPLETED: "COMPLETED";
  readonly FAILED: "FAILED";
  readonly PREPROCESSING: "PREPROCESSING";
  readonly SCHEDULED: "SCHEDULED";
  readonly TESTING: "TESTING";
};
export type AutomatedReasoningPolicyBuildWorkflowStatus =
  (typeof AutomatedReasoningPolicyBuildWorkflowStatus)[keyof typeof AutomatedReasoningPolicyBuildWorkflowStatus];
export interface GetAutomatedReasoningPolicyBuildWorkflowResponse {
  policyArn: string | undefined;
  buildWorkflowId: string | undefined;
  status: AutomatedReasoningPolicyBuildWorkflowStatus | undefined;
  buildWorkflowType: AutomatedReasoningPolicyBuildWorkflowType | undefined;
  documentName?: string | undefined;
  documentContentType?:
    | AutomatedReasoningPolicyBuildDocumentContentType
    | undefined;
  documentDescription?: string | undefined;
  createdAt: Date | undefined;
  updatedAt: Date | undefined;
}
export declare const AutomatedReasoningPolicyBuildResultAssetType: {
  readonly BUILD_LOG: "BUILD_LOG";
  readonly POLICY_DEFINITION: "POLICY_DEFINITION";
  readonly QUALITY_REPORT: "QUALITY_REPORT";
};
export type AutomatedReasoningPolicyBuildResultAssetType =
  (typeof AutomatedReasoningPolicyBuildResultAssetType)[keyof typeof AutomatedReasoningPolicyBuildResultAssetType];
export interface GetAutomatedReasoningPolicyBuildWorkflowResultAssetsRequest {
  policyArn: string | undefined;
  buildWorkflowId: string | undefined;
  assetType: AutomatedReasoningPolicyBuildResultAssetType | undefined;
}
export interface AutomatedReasoningPolicyAddRuleMutation {
  rule: AutomatedReasoningPolicyDefinitionRule | undefined;
}
export interface AutomatedReasoningPolicyAddTypeMutation {
  type: AutomatedReasoningPolicyDefinitionType | undefined;
}
export interface AutomatedReasoningPolicyAddVariableMutation {
  variable: AutomatedReasoningPolicyDefinitionVariable | undefined;
}
export interface AutomatedReasoningPolicyDeleteRuleMutation {
  id: string | undefined;
}
export interface AutomatedReasoningPolicyDeleteTypeMutation {
  name: string | undefined;
}
export interface AutomatedReasoningPolicyDeleteVariableMutation {
  name: string | undefined;
}
export interface AutomatedReasoningPolicyUpdateRuleMutation {
  rule: AutomatedReasoningPolicyDefinitionRule | undefined;
}
export interface AutomatedReasoningPolicyUpdateTypeMutation {
  type: AutomatedReasoningPolicyDefinitionType | undefined;
}
export interface AutomatedReasoningPolicyUpdateVariableMutation {
  variable: AutomatedReasoningPolicyDefinitionVariable | undefined;
}
export type AutomatedReasoningPolicyMutation =
  | AutomatedReasoningPolicyMutation.AddRuleMember
  | AutomatedReasoningPolicyMutation.AddTypeMember
  | AutomatedReasoningPolicyMutation.AddVariableMember
  | AutomatedReasoningPolicyMutation.DeleteRuleMember
  | AutomatedReasoningPolicyMutation.DeleteTypeMember
  | AutomatedReasoningPolicyMutation.DeleteVariableMember
  | AutomatedReasoningPolicyMutation.UpdateRuleMember
  | AutomatedReasoningPolicyMutation.UpdateTypeMember
  | AutomatedReasoningPolicyMutation.UpdateVariableMember
  | AutomatedReasoningPolicyMutation.$UnknownMember;
export declare namespace AutomatedReasoningPolicyMutation {
  interface AddTypeMember {
    addType: AutomatedReasoningPolicyAddTypeMutation;
    updateType?: never;
    deleteType?: never;
    addVariable?: never;
    updateVariable?: never;
    deleteVariable?: never;
    addRule?: never;
    updateRule?: never;
    deleteRule?: never;
    $unknown?: never;
  }
  interface UpdateTypeMember {
    addType?: never;
    updateType: AutomatedReasoningPolicyUpdateTypeMutation;
    deleteType?: never;
    addVariable?: never;
    updateVariable?: never;
    deleteVariable?: never;
    addRule?: never;
    updateRule?: never;
    deleteRule?: never;
    $unknown?: never;
  }
  interface DeleteTypeMember {
    addType?: never;
    updateType?: never;
    deleteType: AutomatedReasoningPolicyDeleteTypeMutation;
    addVariable?: never;
    updateVariable?: never;
    deleteVariable?: never;
    addRule?: never;
    updateRule?: never;
    deleteRule?: never;
    $unknown?: never;
  }
  interface AddVariableMember {
    addType?: never;
    updateType?: never;
    deleteType?: never;
    addVariable: AutomatedReasoningPolicyAddVariableMutation;
    updateVariable?: never;
    deleteVariable?: never;
    addRule?: never;
    updateRule?: never;
    deleteRule?: never;
    $unknown?: never;
  }
  interface UpdateVariableMember {
    addType?: never;
    updateType?: never;
    deleteType?: never;
    addVariable?: never;
    updateVariable: AutomatedReasoningPolicyUpdateVariableMutation;
    deleteVariable?: never;
    addRule?: never;
    updateRule?: never;
    deleteRule?: never;
    $unknown?: never;
  }
  interface DeleteVariableMember {
    addType?: never;
    updateType?: never;
    deleteType?: never;
    addVariable?: never;
    updateVariable?: never;
    deleteVariable: AutomatedReasoningPolicyDeleteVariableMutation;
    addRule?: never;
    updateRule?: never;
    deleteRule?: never;
    $unknown?: never;
  }
  interface AddRuleMember {
    addType?: never;
    updateType?: never;
    deleteType?: never;
    addVariable?: never;
    updateVariable?: never;
    deleteVariable?: never;
    addRule: AutomatedReasoningPolicyAddRuleMutation;
    updateRule?: never;
    deleteRule?: never;
    $unknown?: never;
  }
  interface UpdateRuleMember {
    addType?: never;
    updateType?: never;
    deleteType?: never;
    addVariable?: never;
    updateVariable?: never;
    deleteVariable?: never;
    addRule?: never;
    updateRule: AutomatedReasoningPolicyUpdateRuleMutation;
    deleteRule?: never;
    $unknown?: never;
  }
  interface DeleteRuleMember {
    addType?: never;
    updateType?: never;
    deleteType?: never;
    addVariable?: never;
    updateVariable?: never;
    deleteVariable?: never;
    addRule?: never;
    updateRule?: never;
    deleteRule: AutomatedReasoningPolicyDeleteRuleMutation;
    $unknown?: never;
  }
  interface $UnknownMember {
    addType?: never;
    updateType?: never;
    deleteType?: never;
    addVariable?: never;
    updateVariable?: never;
    deleteVariable?: never;
    addRule?: never;
    updateRule?: never;
    deleteRule?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    addType: (value: AutomatedReasoningPolicyAddTypeMutation) => T;
    updateType: (value: AutomatedReasoningPolicyUpdateTypeMutation) => T;
    deleteType: (value: AutomatedReasoningPolicyDeleteTypeMutation) => T;
    addVariable: (value: AutomatedReasoningPolicyAddVariableMutation) => T;
    updateVariable: (
      value: AutomatedReasoningPolicyUpdateVariableMutation
    ) => T;
    deleteVariable: (
      value: AutomatedReasoningPolicyDeleteVariableMutation
    ) => T;
    addRule: (value: AutomatedReasoningPolicyAddRuleMutation) => T;
    updateRule: (value: AutomatedReasoningPolicyUpdateRuleMutation) => T;
    deleteRule: (value: AutomatedReasoningPolicyDeleteRuleMutation) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(
    value: AutomatedReasoningPolicyMutation,
    visitor: Visitor<T>
  ) => T;
}
export interface AutomatedReasoningPolicyPlanning {}
export type AutomatedReasoningPolicyBuildStepContext =
  | AutomatedReasoningPolicyBuildStepContext.MutationMember
  | AutomatedReasoningPolicyBuildStepContext.PlanningMember
  | AutomatedReasoningPolicyBuildStepContext.$UnknownMember;
export declare namespace AutomatedReasoningPolicyBuildStepContext {
  interface PlanningMember {
    planning: AutomatedReasoningPolicyPlanning;
    mutation?: never;
    $unknown?: never;
  }
  interface MutationMember {
    planning?: never;
    mutation: AutomatedReasoningPolicyMutation;
    $unknown?: never;
  }
  interface $UnknownMember {
    planning?: never;
    mutation?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    planning: (value: AutomatedReasoningPolicyPlanning) => T;
    mutation: (value: AutomatedReasoningPolicyMutation) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(
    value: AutomatedReasoningPolicyBuildStepContext,
    visitor: Visitor<T>
  ) => T;
}
export declare const AutomatedReasoningPolicyBuildMessageType: {
  readonly ERROR: "ERROR";
  readonly INFO: "INFO";
  readonly WARNING: "WARNING";
};
export type AutomatedReasoningPolicyBuildMessageType =
  (typeof AutomatedReasoningPolicyBuildMessageType)[keyof typeof AutomatedReasoningPolicyBuildMessageType];
export interface AutomatedReasoningPolicyBuildStepMessage {
  message: string | undefined;
  messageType: AutomatedReasoningPolicyBuildMessageType | undefined;
}
export type AutomatedReasoningPolicyDefinitionElement =
  | AutomatedReasoningPolicyDefinitionElement.PolicyDefinitionRuleMember
  | AutomatedReasoningPolicyDefinitionElement.PolicyDefinitionTypeMember
  | AutomatedReasoningPolicyDefinitionElement.PolicyDefinitionVariableMember
  | AutomatedReasoningPolicyDefinitionElement.$UnknownMember;
export declare namespace AutomatedReasoningPolicyDefinitionElement {
  interface PolicyDefinitionVariableMember {
    policyDefinitionVariable: AutomatedReasoningPolicyDefinitionVariable;
    policyDefinitionType?: never;
    policyDefinitionRule?: never;
    $unknown?: never;
  }
  interface PolicyDefinitionTypeMember {
    policyDefinitionVariable?: never;
    policyDefinitionType: AutomatedReasoningPolicyDefinitionType;
    policyDefinitionRule?: never;
    $unknown?: never;
  }
  interface PolicyDefinitionRuleMember {
    policyDefinitionVariable?: never;
    policyDefinitionType?: never;
    policyDefinitionRule: AutomatedReasoningPolicyDefinitionRule;
    $unknown?: never;
  }
  interface $UnknownMember {
    policyDefinitionVariable?: never;
    policyDefinitionType?: never;
    policyDefinitionRule?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    policyDefinitionVariable: (
      value: AutomatedReasoningPolicyDefinitionVariable
    ) => T;
    policyDefinitionType: (value: AutomatedReasoningPolicyDefinitionType) => T;
    policyDefinitionRule: (value: AutomatedReasoningPolicyDefinitionRule) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(
    value: AutomatedReasoningPolicyDefinitionElement,
    visitor: Visitor<T>
  ) => T;
}
export interface AutomatedReasoningPolicyBuildStep {
  context: AutomatedReasoningPolicyBuildStepContext | undefined;
  priorElement?: AutomatedReasoningPolicyDefinitionElement | undefined;
  messages: AutomatedReasoningPolicyBuildStepMessage[] | undefined;
}
export declare const AutomatedReasoningPolicyAnnotationStatus: {
  readonly APPLIED: "APPLIED";
  readonly FAILED: "FAILED";
};
export type AutomatedReasoningPolicyAnnotationStatus =
  (typeof AutomatedReasoningPolicyAnnotationStatus)[keyof typeof AutomatedReasoningPolicyAnnotationStatus];
export interface AutomatedReasoningPolicyBuildLogEntry {
  annotation: AutomatedReasoningPolicyAnnotation | undefined;
  status: AutomatedReasoningPolicyAnnotationStatus | undefined;
  buildSteps: AutomatedReasoningPolicyBuildStep[] | undefined;
}
export interface AutomatedReasoningPolicyBuildLog {
  entries: AutomatedReasoningPolicyBuildLogEntry[] | undefined;
}
export interface AutomatedReasoningPolicyDisjointRuleSet {
  variables: string[] | undefined;
  rules: string[] | undefined;
}
export interface AutomatedReasoningPolicyDefinitionTypeValuePair {
  typeName: string | undefined;
  valueName: string | undefined;
}
export interface AutomatedReasoningPolicyDefinitionQualityReport {
  typeCount: number | undefined;
  variableCount: number | undefined;
  ruleCount: number | undefined;
  unusedTypes: string[] | undefined;
  unusedTypeValues:
    | AutomatedReasoningPolicyDefinitionTypeValuePair[]
    | undefined;
  unusedVariables: string[] | undefined;
  conflictingRules: string[] | undefined;
  disjointRuleSets: AutomatedReasoningPolicyDisjointRuleSet[] | undefined;
}
export type AutomatedReasoningPolicyBuildResultAssets =
  | AutomatedReasoningPolicyBuildResultAssets.BuildLogMember
  | AutomatedReasoningPolicyBuildResultAssets.PolicyDefinitionMember
  | AutomatedReasoningPolicyBuildResultAssets.QualityReportMember
  | AutomatedReasoningPolicyBuildResultAssets.$UnknownMember;
export declare namespace AutomatedReasoningPolicyBuildResultAssets {
  interface PolicyDefinitionMember {
    policyDefinition: AutomatedReasoningPolicyDefinition;
    qualityReport?: never;
    buildLog?: never;
    $unknown?: never;
  }
  interface QualityReportMember {
    policyDefinition?: never;
    qualityReport: AutomatedReasoningPolicyDefinitionQualityReport;
    buildLog?: never;
    $unknown?: never;
  }
  interface BuildLogMember {
    policyDefinition?: never;
    qualityReport?: never;
    buildLog: AutomatedReasoningPolicyBuildLog;
    $unknown?: never;
  }
  interface $UnknownMember {
    policyDefinition?: never;
    qualityReport?: never;
    buildLog?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    policyDefinition: (value: AutomatedReasoningPolicyDefinition) => T;
    qualityReport: (
      value: AutomatedReasoningPolicyDefinitionQualityReport
    ) => T;
    buildLog: (value: AutomatedReasoningPolicyBuildLog) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(
    value: AutomatedReasoningPolicyBuildResultAssets,
    visitor: Visitor<T>
  ) => T;
}
export interface GetAutomatedReasoningPolicyBuildWorkflowResultAssetsResponse {
  policyArn: string | undefined;
  buildWorkflowId: string | undefined;
  buildWorkflowAssets?: AutomatedReasoningPolicyBuildResultAssets | undefined;
}
export interface GetAutomatedReasoningPolicyNextScenarioRequest {
  policyArn: string | undefined;
  buildWorkflowId: string | undefined;
}
export interface AutomatedReasoningPolicyScenario {
  expression: string | undefined;
  alternateExpression: string | undefined;
  ruleIds: string[] | undefined;
  expectedResult: AutomatedReasoningCheckResult | undefined;
}
export interface GetAutomatedReasoningPolicyNextScenarioResponse {
  policyArn: string | undefined;
  scenario?: AutomatedReasoningPolicyScenario | undefined;
}
export interface GetAutomatedReasoningPolicyTestCaseRequest {
  policyArn: string | undefined;
  testCaseId: string | undefined;
}
export interface AutomatedReasoningPolicyTestCase {
  testCaseId: string | undefined;
  guardContent: string | undefined;
  queryContent?: string | undefined;
  expectedAggregatedFindingsResult?: AutomatedReasoningCheckResult | undefined;
  createdAt: Date | undefined;
  updatedAt: Date | undefined;
  confidenceThreshold?: number | undefined;
}
export interface GetAutomatedReasoningPolicyTestCaseResponse {
  policyArn: string | undefined;
  testCase: AutomatedReasoningPolicyTestCase | undefined;
}
export interface GetAutomatedReasoningPolicyTestResultRequest {
  policyArn: string | undefined;
  buildWorkflowId: string | undefined;
  testCaseId: string | undefined;
}
export interface AutomatedReasoningCheckRule {
  id?: string | undefined;
  policyVersionArn?: string | undefined;
}
export interface AutomatedReasoningLogicStatement {
  logic: string | undefined;
  naturalLanguage?: string | undefined;
}
export declare const AutomatedReasoningCheckLogicWarningType: {
  readonly ALWAYS_FALSE: "ALWAYS_FALSE";
  readonly ALWAYS_TRUE: "ALWAYS_TRUE";
};
export type AutomatedReasoningCheckLogicWarningType =
  (typeof AutomatedReasoningCheckLogicWarningType)[keyof typeof AutomatedReasoningCheckLogicWarningType];
export interface AutomatedReasoningCheckLogicWarning {
  type?: AutomatedReasoningCheckLogicWarningType | undefined;
  premises?: AutomatedReasoningLogicStatement[] | undefined;
  claims?: AutomatedReasoningLogicStatement[] | undefined;
}
export interface AutomatedReasoningCheckInputTextReference {
  text?: string | undefined;
}
export interface AutomatedReasoningCheckTranslation {
  premises?: AutomatedReasoningLogicStatement[] | undefined;
  claims: AutomatedReasoningLogicStatement[] | undefined;
  untranslatedPremises?:
    | AutomatedReasoningCheckInputTextReference[]
    | undefined;
  untranslatedClaims?: AutomatedReasoningCheckInputTextReference[] | undefined;
  confidence: number | undefined;
}
export interface AutomatedReasoningCheckImpossibleFinding {
  translation?: AutomatedReasoningCheckTranslation | undefined;
  contradictingRules?: AutomatedReasoningCheckRule[] | undefined;
  logicWarning?: AutomatedReasoningCheckLogicWarning | undefined;
}
export interface AutomatedReasoningCheckInvalidFinding {
  translation?: AutomatedReasoningCheckTranslation | undefined;
  contradictingRules?: AutomatedReasoningCheckRule[] | undefined;
  logicWarning?: AutomatedReasoningCheckLogicWarning | undefined;
}
export interface AutomatedReasoningCheckNoTranslationsFinding {}
export interface AutomatedReasoningCheckScenario {
  statements?: AutomatedReasoningLogicStatement[] | undefined;
}
export interface AutomatedReasoningCheckSatisfiableFinding {
  translation?: AutomatedReasoningCheckTranslation | undefined;
  claimsTrueScenario?: AutomatedReasoningCheckScenario | undefined;
  claimsFalseScenario?: AutomatedReasoningCheckScenario | undefined;
  logicWarning?: AutomatedReasoningCheckLogicWarning | undefined;
}
export interface AutomatedReasoningCheckTooComplexFinding {}
export interface AutomatedReasoningCheckTranslationOption {
  translations?: AutomatedReasoningCheckTranslation[] | undefined;
}
export interface AutomatedReasoningCheckTranslationAmbiguousFinding {
  options?: AutomatedReasoningCheckTranslationOption[] | undefined;
  differenceScenarios?: AutomatedReasoningCheckScenario[] | undefined;
}
export interface AutomatedReasoningCheckValidFinding {
  translation?: AutomatedReasoningCheckTranslation | undefined;
  claimsTrueScenario?: AutomatedReasoningCheckScenario | undefined;
  supportingRules?: AutomatedReasoningCheckRule[] | undefined;
  logicWarning?: AutomatedReasoningCheckLogicWarning | undefined;
}
export type AutomatedReasoningCheckFinding =
  | AutomatedReasoningCheckFinding.ImpossibleMember
  | AutomatedReasoningCheckFinding.InvalidMember
  | AutomatedReasoningCheckFinding.NoTranslationsMember
  | AutomatedReasoningCheckFinding.SatisfiableMember
  | AutomatedReasoningCheckFinding.TooComplexMember
  | AutomatedReasoningCheckFinding.TranslationAmbiguousMember
  | AutomatedReasoningCheckFinding.ValidMember
  | AutomatedReasoningCheckFinding.$UnknownMember;
export declare namespace AutomatedReasoningCheckFinding {
  interface ValidMember {
    valid: AutomatedReasoningCheckValidFinding;
    invalid?: never;
    satisfiable?: never;
    impossible?: never;
    translationAmbiguous?: never;
    tooComplex?: never;
    noTranslations?: never;
    $unknown?: never;
  }
  interface InvalidMember {
    valid?: never;
    invalid: AutomatedReasoningCheckInvalidFinding;
    satisfiable?: never;
    impossible?: never;
    translationAmbiguous?: never;
    tooComplex?: never;
    noTranslations?: never;
    $unknown?: never;
  }
  interface SatisfiableMember {
    valid?: never;
    invalid?: never;
    satisfiable: AutomatedReasoningCheckSatisfiableFinding;
    impossible?: never;
    translationAmbiguous?: never;
    tooComplex?: never;
    noTranslations?: never;
    $unknown?: never;
  }
  interface ImpossibleMember {
    valid?: never;
    invalid?: never;
    satisfiable?: never;
    impossible: AutomatedReasoningCheckImpossibleFinding;
    translationAmbiguous?: never;
    tooComplex?: never;
    noTranslations?: never;
    $unknown?: never;
  }
  interface TranslationAmbiguousMember {
    valid?: never;
    invalid?: never;
    satisfiable?: never;
    impossible?: never;
    translationAmbiguous: AutomatedReasoningCheckTranslationAmbiguousFinding;
    tooComplex?: never;
    noTranslations?: never;
    $unknown?: never;
  }
  interface TooComplexMember {
    valid?: never;
    invalid?: never;
    satisfiable?: never;
    impossible?: never;
    translationAmbiguous?: never;
    tooComplex: AutomatedReasoningCheckTooComplexFinding;
    noTranslations?: never;
    $unknown?: never;
  }
  interface NoTranslationsMember {
    valid?: never;
    invalid?: never;
    satisfiable?: never;
    impossible?: never;
    translationAmbiguous?: never;
    tooComplex?: never;
    noTranslations: AutomatedReasoningCheckNoTranslationsFinding;
    $unknown?: never;
  }
  interface $UnknownMember {
    valid?: never;
    invalid?: never;
    satisfiable?: never;
    impossible?: never;
    translationAmbiguous?: never;
    tooComplex?: never;
    noTranslations?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    valid: (value: AutomatedReasoningCheckValidFinding) => T;
    invalid: (value: AutomatedReasoningCheckInvalidFinding) => T;
    satisfiable: (value: AutomatedReasoningCheckSatisfiableFinding) => T;
    impossible: (value: AutomatedReasoningCheckImpossibleFinding) => T;
    translationAmbiguous: (
      value: AutomatedReasoningCheckTranslationAmbiguousFinding
    ) => T;
    tooComplex: (value: AutomatedReasoningCheckTooComplexFinding) => T;
    noTranslations: (value: AutomatedReasoningCheckNoTranslationsFinding) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(
    value: AutomatedReasoningCheckFinding,
    visitor: Visitor<T>
  ) => T;
}
export declare const AutomatedReasoningPolicyTestRunResult: {
  readonly FAILED: "FAILED";
  readonly PASSED: "PASSED";
};
export type AutomatedReasoningPolicyTestRunResult =
  (typeof AutomatedReasoningPolicyTestRunResult)[keyof typeof AutomatedReasoningPolicyTestRunResult];
export declare const AutomatedReasoningPolicyTestRunStatus: {
  readonly COMPLETED: "COMPLETED";
  readonly FAILED: "FAILED";
  readonly IN_PROGRESS: "IN_PROGRESS";
  readonly NOT_STARTED: "NOT_STARTED";
  readonly SCHEDULED: "SCHEDULED";
};
export type AutomatedReasoningPolicyTestRunStatus =
  (typeof AutomatedReasoningPolicyTestRunStatus)[keyof typeof AutomatedReasoningPolicyTestRunStatus];
export interface AutomatedReasoningPolicyTestResult {
  testCase: AutomatedReasoningPolicyTestCase | undefined;
  policyArn: string | undefined;
  testRunStatus: AutomatedReasoningPolicyTestRunStatus | undefined;
  testFindings?: AutomatedReasoningCheckFinding[] | undefined;
  testRunResult?: AutomatedReasoningPolicyTestRunResult | undefined;
  aggregatedTestFindingsResult?: AutomatedReasoningCheckResult | undefined;
  updatedAt: Date | undefined;
}
export interface GetAutomatedReasoningPolicyTestResultResponse {
  testResult: AutomatedReasoningPolicyTestResult | undefined;
}
export interface ListAutomatedReasoningPoliciesRequest {
  policyArn?: string | undefined;
  nextToken?: string | undefined;
  maxResults?: number | undefined;
}
export interface AutomatedReasoningPolicySummary {
  policyArn: string | undefined;
  name: string | undefined;
  description?: string | undefined;
  version: string | undefined;
  policyId: string | undefined;
  createdAt: Date | undefined;
  updatedAt: Date | undefined;
}
export interface ListAutomatedReasoningPoliciesResponse {
  automatedReasoningPolicySummaries:
    | AutomatedReasoningPolicySummary[]
    | undefined;
  nextToken?: string | undefined;
}
export interface ListAutomatedReasoningPolicyBuildWorkflowsRequest {
  policyArn: string | undefined;
  nextToken?: string | undefined;
  maxResults?: number | undefined;
}
export interface AutomatedReasoningPolicyBuildWorkflowSummary {
  policyArn: string | undefined;
  buildWorkflowId: string | undefined;
  status: AutomatedReasoningPolicyBuildWorkflowStatus | undefined;
  buildWorkflowType: AutomatedReasoningPolicyBuildWorkflowType | undefined;
  createdAt: Date | undefined;
  updatedAt: Date | undefined;
}
export interface ListAutomatedReasoningPolicyBuildWorkflowsResponse {
  automatedReasoningPolicyBuildWorkflowSummaries:
    | AutomatedReasoningPolicyBuildWorkflowSummary[]
    | undefined;
  nextToken?: string | undefined;
}
export interface ListAutomatedReasoningPolicyTestCasesRequest {
  policyArn: string | undefined;
  nextToken?: string | undefined;
  maxResults?: number | undefined;
}
export interface ListAutomatedReasoningPolicyTestCasesResponse {
  testCases: AutomatedReasoningPolicyTestCase[] | undefined;
  nextToken?: string | undefined;
}
export interface ListAutomatedReasoningPolicyTestResultsRequest {
  policyArn: string | undefined;
  buildWorkflowId: string | undefined;
  nextToken?: string | undefined;
  maxResults?: number | undefined;
}
export interface ListAutomatedReasoningPolicyTestResultsResponse {
  testResults: AutomatedReasoningPolicyTestResult[] | undefined;
  nextToken?: string | undefined;
}
export interface AutomatedReasoningPolicyBuildWorkflowDocument {
  document: Uint8Array | undefined;
  documentContentType:
    | AutomatedReasoningPolicyBuildDocumentContentType
    | undefined;
  documentName: string | undefined;
  documentDescription?: string | undefined;
}
export interface AutomatedReasoningPolicyBuildWorkflowRepairContent {
  annotations: AutomatedReasoningPolicyAnnotation[] | undefined;
}
export type AutomatedReasoningPolicyWorkflowTypeContent =
  | AutomatedReasoningPolicyWorkflowTypeContent.DocumentsMember
  | AutomatedReasoningPolicyWorkflowTypeContent.PolicyRepairAssetsMember
  | AutomatedReasoningPolicyWorkflowTypeContent.$UnknownMember;
export declare namespace AutomatedReasoningPolicyWorkflowTypeContent {
  interface DocumentsMember {
    documents: AutomatedReasoningPolicyBuildWorkflowDocument[];
    policyRepairAssets?: never;
    $unknown?: never;
  }
  interface PolicyRepairAssetsMember {
    documents?: never;
    policyRepairAssets: AutomatedReasoningPolicyBuildWorkflowRepairContent;
    $unknown?: never;
  }
  interface $UnknownMember {
    documents?: never;
    policyRepairAssets?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    documents: (value: AutomatedReasoningPolicyBuildWorkflowDocument[]) => T;
    policyRepairAssets: (
      value: AutomatedReasoningPolicyBuildWorkflowRepairContent
    ) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(
    value: AutomatedReasoningPolicyWorkflowTypeContent,
    visitor: Visitor<T>
  ) => T;
}
export interface AutomatedReasoningPolicyBuildWorkflowSource {
  policyDefinition?: AutomatedReasoningPolicyDefinition | undefined;
  workflowContent?: AutomatedReasoningPolicyWorkflowTypeContent | undefined;
}
export interface StartAutomatedReasoningPolicyBuildWorkflowRequest {
  policyArn: string | undefined;
  buildWorkflowType: AutomatedReasoningPolicyBuildWorkflowType | undefined;
  clientRequestToken?: string | undefined;
  sourceContent: AutomatedReasoningPolicyBuildWorkflowSource | undefined;
}
export interface StartAutomatedReasoningPolicyBuildWorkflowResponse {
  policyArn: string | undefined;
  buildWorkflowId: string | undefined;
}
export interface StartAutomatedReasoningPolicyTestWorkflowRequest {
  policyArn: string | undefined;
  buildWorkflowId: string | undefined;
  testCaseIds?: string[] | undefined;
  clientRequestToken?: string | undefined;
}
export interface StartAutomatedReasoningPolicyTestWorkflowResponse {
  policyArn: string | undefined;
}
export interface UpdateAutomatedReasoningPolicyRequest {
  policyArn: string | undefined;
  policyDefinition: AutomatedReasoningPolicyDefinition | undefined;
  name?: string | undefined;
  description?: string | undefined;
}
export interface UpdateAutomatedReasoningPolicyResponse {
  policyArn: string | undefined;
  name: string | undefined;
  definitionHash: string | undefined;
  updatedAt: Date | undefined;
}
export interface UpdateAutomatedReasoningPolicyAnnotationsRequest {
  policyArn: string | undefined;
  buildWorkflowId: string | undefined;
  annotations: AutomatedReasoningPolicyAnnotation[] | undefined;
  lastUpdatedAnnotationSetHash: string | undefined;
}
export interface UpdateAutomatedReasoningPolicyAnnotationsResponse {
  policyArn: string | undefined;
  buildWorkflowId: string | undefined;
  annotationSetHash: string | undefined;
  updatedAt: Date | undefined;
}
export interface UpdateAutomatedReasoningPolicyTestCaseRequest {
  policyArn: string | undefined;
  testCaseId: string | undefined;
  guardContent: string | undefined;
  queryContent?: string | undefined;
  lastUpdatedAt: Date | undefined;
  expectedAggregatedFindingsResult: AutomatedReasoningCheckResult | undefined;
  confidenceThreshold?: number | undefined;
  kmsKeyArn?: string | undefined;
  clientRequestToken?: string | undefined;
}
export interface UpdateAutomatedReasoningPolicyTestCaseResponse {
  policyArn: string | undefined;
  testCaseId: string | undefined;
}
export interface VpcConfig {
  subnetIds: string[] | undefined;
  securityGroupIds: string[] | undefined;
}
export interface SageMakerEndpoint {
  initialInstanceCount: number | undefined;
  instanceType: string | undefined;
  executionRole: string | undefined;
  kmsEncryptionKey?: string | undefined;
  vpc?: VpcConfig | undefined;
}
export type EndpointConfig =
  | EndpointConfig.SageMakerMember
  | EndpointConfig.$UnknownMember;
export declare namespace EndpointConfig {
  interface SageMakerMember {
    sageMaker: SageMakerEndpoint;
    $unknown?: never;
  }
  interface $UnknownMember {
    sageMaker?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    sageMaker: (value: SageMakerEndpoint) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: EndpointConfig, visitor: Visitor<T>) => T;
}
export interface CreateMarketplaceModelEndpointRequest {
  modelSourceIdentifier: string | undefined;
  endpointConfig: EndpointConfig | undefined;
  acceptEula?: boolean | undefined;
  endpointName: string | undefined;
  clientRequestToken?: string | undefined;
  tags?: Tag[] | undefined;
}
export declare const Status: {
  readonly INCOMPATIBLE_ENDPOINT: "INCOMPATIBLE_ENDPOINT";
  readonly REGISTERED: "REGISTERED";
};
export type Status = (typeof Status)[keyof typeof Status];
export interface MarketplaceModelEndpoint {
  endpointArn: string | undefined;
  modelSourceIdentifier: string | undefined;
  status?: Status | undefined;
  statusMessage?: string | undefined;
  createdAt: Date | undefined;
  updatedAt: Date | undefined;
  endpointConfig: EndpointConfig | undefined;
  endpointStatus: string | undefined;
  endpointStatusMessage?: string | undefined;
}
export interface CreateMarketplaceModelEndpointResponse {
  marketplaceModelEndpoint: MarketplaceModelEndpoint | undefined;
}
export interface DeleteMarketplaceModelEndpointRequest {
  endpointArn: string | undefined;
}
export interface DeleteMarketplaceModelEndpointResponse {}
export interface DeregisterMarketplaceModelEndpointRequest {
  endpointArn: string | undefined;
}
export interface DeregisterMarketplaceModelEndpointResponse {}
export declare class ServiceUnavailableException extends __BaseException {
  readonly name: "ServiceUnavailableException";
  readonly $fault: "server";
  constructor(
    opts: __ExceptionOptionType<ServiceUnavailableException, __BaseException>
  );
}
export interface GetMarketplaceModelEndpointRequest {
  endpointArn: string | undefined;
}
export interface GetMarketplaceModelEndpointResponse {
  marketplaceModelEndpoint?: MarketplaceModelEndpoint | undefined;
}
export interface ListMarketplaceModelEndpointsRequest {
  maxResults?: number | undefined;
  nextToken?: string | undefined;
  modelSourceEquals?: string | undefined;
}
export interface MarketplaceModelEndpointSummary {
  endpointArn: string | undefined;
  modelSourceIdentifier: string | undefined;
  status?: Status | undefined;
  statusMessage?: string | undefined;
  createdAt: Date | undefined;
  updatedAt: Date | undefined;
}
export interface ListMarketplaceModelEndpointsResponse {
  marketplaceModelEndpoints?: MarketplaceModelEndpointSummary[] | undefined;
  nextToken?: string | undefined;
}
export interface RegisterMarketplaceModelEndpointRequest {
  endpointIdentifier: string | undefined;
  modelSourceIdentifier: string | undefined;
}
export interface RegisterMarketplaceModelEndpointResponse {
  marketplaceModelEndpoint: MarketplaceModelEndpoint | undefined;
}
export interface UpdateMarketplaceModelEndpointRequest {
  endpointArn: string | undefined;
  endpointConfig: EndpointConfig | undefined;
  clientRequestToken?: string | undefined;
}
export interface UpdateMarketplaceModelEndpointResponse {
  marketplaceModelEndpoint: MarketplaceModelEndpoint | undefined;
}
export interface CreateCustomModelDeploymentRequest {
  modelDeploymentName: string | undefined;
  modelArn: string | undefined;
  description?: string | undefined;
  tags?: Tag[] | undefined;
  clientRequestToken?: string | undefined;
}
export interface CreateCustomModelDeploymentResponse {
  customModelDeploymentArn: string | undefined;
}
export interface DeleteCustomModelDeploymentRequest {
  customModelDeploymentIdentifier: string | undefined;
}
export interface DeleteCustomModelDeploymentResponse {}
export interface GetCustomModelDeploymentRequest {
  customModelDeploymentIdentifier: string | undefined;
}
export declare const CustomModelDeploymentStatus: {
  readonly ACTIVE: "Active";
  readonly CREATING: "Creating";
  readonly FAILED: "Failed";
};
export type CustomModelDeploymentStatus =
  (typeof CustomModelDeploymentStatus)[keyof typeof CustomModelDeploymentStatus];
export interface GetCustomModelDeploymentResponse {
  customModelDeploymentArn: string | undefined;
  modelDeploymentName: string | undefined;
  modelArn: string | undefined;
  createdAt: Date | undefined;
  status: CustomModelDeploymentStatus | undefined;
  description?: string | undefined;
  failureMessage?: string | undefined;
  lastUpdatedAt?: Date | undefined;
}
export declare const SortModelsBy: {
  readonly CREATION_TIME: "CreationTime";
};
export type SortModelsBy = (typeof SortModelsBy)[keyof typeof SortModelsBy];
export declare const SortOrder: {
  readonly ASCENDING: "Ascending";
  readonly DESCENDING: "Descending";
};
export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder];
export interface ListCustomModelDeploymentsRequest {
  createdBefore?: Date | undefined;
  createdAfter?: Date | undefined;
  nameContains?: string | undefined;
  maxResults?: number | undefined;
  nextToken?: string | undefined;
  sortBy?: SortModelsBy | undefined;
  sortOrder?: SortOrder | undefined;
  statusEquals?: CustomModelDeploymentStatus | undefined;
  modelArnEquals?: string | undefined;
}
export interface CustomModelDeploymentSummary {
  customModelDeploymentArn: string | undefined;
  customModelDeploymentName: string | undefined;
  modelArn: string | undefined;
  createdAt: Date | undefined;
  status: CustomModelDeploymentStatus | undefined;
  lastUpdatedAt?: Date | undefined;
  failureMessage?: string | undefined;
}
export interface ListCustomModelDeploymentsResponse {
  nextToken?: string | undefined;
  modelDeploymentSummaries?: CustomModelDeploymentSummary[] | undefined;
}
export interface S3DataSource {
  s3Uri: string | undefined;
}
export type ModelDataSource =
  | ModelDataSource.S3DataSourceMember
  | ModelDataSource.$UnknownMember;
export declare namespace ModelDataSource {
  interface S3DataSourceMember {
    s3DataSource: S3DataSource;
    $unknown?: never;
  }
  interface $UnknownMember {
    s3DataSource?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    s3DataSource: (value: S3DataSource) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: ModelDataSource, visitor: Visitor<T>) => T;
}
export interface CreateCustomModelRequest {
  modelName: string | undefined;
  modelSourceConfig: ModelDataSource | undefined;
  modelKmsKeyArn?: string | undefined;
  roleArn?: string | undefined;
  modelTags?: Tag[] | undefined;
  clientRequestToken?: string | undefined;
}
export interface CreateCustomModelResponse {
  modelArn: string | undefined;
}
export interface DeleteCustomModelRequest {
  modelIdentifier: string | undefined;
}
export interface DeleteCustomModelResponse {}
export interface GetCustomModelRequest {
  modelIdentifier: string | undefined;
}
export interface TeacherModelConfig {
  teacherModelIdentifier: string | undefined;
  maxResponseLengthForInference?: number | undefined;
}
export interface DistillationConfig {
  teacherModelConfig: TeacherModelConfig | undefined;
}
export type CustomizationConfig =
  | CustomizationConfig.DistillationConfigMember
  | CustomizationConfig.$UnknownMember;
export declare namespace CustomizationConfig {
  interface DistillationConfigMember {
    distillationConfig: DistillationConfig;
    $unknown?: never;
  }
  interface $UnknownMember {
    distillationConfig?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    distillationConfig: (value: DistillationConfig) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: CustomizationConfig, visitor: Visitor<T>) => T;
}
export declare const CustomizationType: {
  readonly CONTINUED_PRE_TRAINING: "CONTINUED_PRE_TRAINING";
  readonly DISTILLATION: "DISTILLATION";
  readonly FINE_TUNING: "FINE_TUNING";
  readonly IMPORTED: "IMPORTED";
};
export type CustomizationType =
  (typeof CustomizationType)[keyof typeof CustomizationType];
export declare const ModelStatus: {
  readonly ACTIVE: "Active";
  readonly CREATING: "Creating";
  readonly FAILED: "Failed";
};
export type ModelStatus = (typeof ModelStatus)[keyof typeof ModelStatus];
export interface OutputDataConfig {
  s3Uri: string | undefined;
}
export type InvocationLogSource =
  | InvocationLogSource.S3UriMember
  | InvocationLogSource.$UnknownMember;
export declare namespace InvocationLogSource {
  interface S3UriMember {
    s3Uri: string;
    $unknown?: never;
  }
  interface $UnknownMember {
    s3Uri?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    s3Uri: (value: string) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: InvocationLogSource, visitor: Visitor<T>) => T;
}
export interface RequestMetadataBaseFilters {
  equals?: Record<string, string> | undefined;
  notEquals?: Record<string, string> | undefined;
}
export type RequestMetadataFilters =
  | RequestMetadataFilters.AndAllMember
  | RequestMetadataFilters.EqualsMember
  | RequestMetadataFilters.NotEqualsMember
  | RequestMetadataFilters.OrAllMember
  | RequestMetadataFilters.$UnknownMember;
export declare namespace RequestMetadataFilters {
  interface EqualsMember {
    equals: Record<string, string>;
    notEquals?: never;
    andAll?: never;
    orAll?: never;
    $unknown?: never;
  }
  interface NotEqualsMember {
    equals?: never;
    notEquals: Record<string, string>;
    andAll?: never;
    orAll?: never;
    $unknown?: never;
  }
  interface AndAllMember {
    equals?: never;
    notEquals?: never;
    andAll: RequestMetadataBaseFilters[];
    orAll?: never;
    $unknown?: never;
  }
  interface OrAllMember {
    equals?: never;
    notEquals?: never;
    andAll?: never;
    orAll: RequestMetadataBaseFilters[];
    $unknown?: never;
  }
  interface $UnknownMember {
    equals?: never;
    notEquals?: never;
    andAll?: never;
    orAll?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    equals: (value: Record<string, string>) => T;
    notEquals: (value: Record<string, string>) => T;
    andAll: (value: RequestMetadataBaseFilters[]) => T;
    orAll: (value: RequestMetadataBaseFilters[]) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: RequestMetadataFilters, visitor: Visitor<T>) => T;
}
export interface InvocationLogsConfig {
  usePromptResponse?: boolean | undefined;
  invocationLogSource: InvocationLogSource | undefined;
  requestMetadataFilters?: RequestMetadataFilters | undefined;
}
export interface TrainingDataConfig {
  s3Uri?: string | undefined;
  invocationLogsConfig?: InvocationLogsConfig | undefined;
}
export interface TrainingMetrics {
  trainingLoss?: number | undefined;
}
export interface Validator {
  s3Uri: string | undefined;
}
export interface ValidationDataConfig {
  validators: Validator[] | undefined;
}
export interface ValidatorMetric {
  validationLoss?: number | undefined;
}
export interface GetCustomModelResponse {
  modelArn: string | undefined;
  modelName: string | undefined;
  jobName?: string | undefined;
  jobArn?: string | undefined;
  baseModelArn?: string | undefined;
  customizationType?: CustomizationType | undefined;
  modelKmsKeyArn?: string | undefined;
  hyperParameters?: Record<string, string> | undefined;
  trainingDataConfig?: TrainingDataConfig | undefined;
  validationDataConfig?: ValidationDataConfig | undefined;
  outputDataConfig?: OutputDataConfig | undefined;
  trainingMetrics?: TrainingMetrics | undefined;
  validationMetrics?: ValidatorMetric[] | undefined;
  creationTime: Date | undefined;
  customizationConfig?: CustomizationConfig | undefined;
  modelStatus?: ModelStatus | undefined;
  failureMessage?: string | undefined;
}
export interface ListCustomModelsRequest {
  creationTimeBefore?: Date | undefined;
  creationTimeAfter?: Date | undefined;
  nameContains?: string | undefined;
  baseModelArnEquals?: string | undefined;
  foundationModelArnEquals?: string | undefined;
  maxResults?: number | undefined;
  nextToken?: string | undefined;
  sortBy?: SortModelsBy | undefined;
  sortOrder?: SortOrder | undefined;
  isOwned?: boolean | undefined;
  modelStatus?: ModelStatus | undefined;
}
export interface CustomModelSummary {
  modelArn: string | undefined;
  modelName: string | undefined;
  creationTime: Date | undefined;
  baseModelArn: string | undefined;
  baseModelName: string | undefined;
  customizationType?: CustomizationType | undefined;
  ownerAccountId?: string | undefined;
  modelStatus?: ModelStatus | undefined;
}
export interface ListCustomModelsResponse {
  nextToken?: string | undefined;
  modelSummaries?: CustomModelSummary[] | undefined;
}
export interface BatchDeleteEvaluationJobRequest {
  jobIdentifiers: string[] | undefined;
}
export interface BatchDeleteEvaluationJobError {
  jobIdentifier: string | undefined;
  code: string | undefined;
  message?: string | undefined;
}
export declare const EvaluationJobStatus: {
  readonly COMPLETED: "Completed";
  readonly DELETING: "Deleting";
  readonly FAILED: "Failed";
  readonly IN_PROGRESS: "InProgress";
  readonly STOPPED: "Stopped";
  readonly STOPPING: "Stopping";
};
export type EvaluationJobStatus =
  (typeof EvaluationJobStatus)[keyof typeof EvaluationJobStatus];
export interface BatchDeleteEvaluationJobItem {
  jobIdentifier: string | undefined;
  jobStatus: EvaluationJobStatus | undefined;
}
export interface BatchDeleteEvaluationJobResponse {
  errors: BatchDeleteEvaluationJobError[] | undefined;
  evaluationJobs: BatchDeleteEvaluationJobItem[] | undefined;
}
export declare const ApplicationType: {
  readonly MODEL_EVALUATION: "ModelEvaluation";
  readonly RAG_EVALUATION: "RagEvaluation";
};
export type ApplicationType =
  (typeof ApplicationType)[keyof typeof ApplicationType];
export type RatingScaleItemValue =
  | RatingScaleItemValue.FloatValueMember
  | RatingScaleItemValue.StringValueMember
  | RatingScaleItemValue.$UnknownMember;
export declare namespace RatingScaleItemValue {
  interface StringValueMember {
    stringValue: string;
    floatValue?: never;
    $unknown?: never;
  }
  interface FloatValueMember {
    stringValue?: never;
    floatValue: number;
    $unknown?: never;
  }
  interface $UnknownMember {
    stringValue?: never;
    floatValue?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    stringValue: (value: string) => T;
    floatValue: (value: number) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: RatingScaleItemValue, visitor: Visitor<T>) => T;
}
export interface RatingScaleItem {
  definition: string | undefined;
  value: RatingScaleItemValue | undefined;
}
export interface CustomMetricDefinition {
  name: string | undefined;
  instructions: string | undefined;
  ratingScale?: RatingScaleItem[] | undefined;
}
export type AutomatedEvaluationCustomMetricSource =
  | AutomatedEvaluationCustomMetricSource.CustomMetricDefinitionMember
  | AutomatedEvaluationCustomMetricSource.$UnknownMember;
export declare namespace AutomatedEvaluationCustomMetricSource {
  interface CustomMetricDefinitionMember {
    customMetricDefinition: CustomMetricDefinition;
    $unknown?: never;
  }
  interface $UnknownMember {
    customMetricDefinition?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    customMetricDefinition: (value: CustomMetricDefinition) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(
    value: AutomatedEvaluationCustomMetricSource,
    visitor: Visitor<T>
  ) => T;
}
export interface CustomMetricBedrockEvaluatorModel {
  modelIdentifier: string | undefined;
}
export interface CustomMetricEvaluatorModelConfig {
  bedrockEvaluatorModels: CustomMetricBedrockEvaluatorModel[] | undefined;
}
export interface AutomatedEvaluationCustomMetricConfig {
  customMetrics: AutomatedEvaluationCustomMetricSource[] | undefined;
  evaluatorModelConfig: CustomMetricEvaluatorModelConfig | undefined;
}
export type EvaluationDatasetLocation =
  | EvaluationDatasetLocation.S3UriMember
  | EvaluationDatasetLocation.$UnknownMember;
export declare namespace EvaluationDatasetLocation {
  interface S3UriMember {
    s3Uri: string;
    $unknown?: never;
  }
  interface $UnknownMember {
    s3Uri?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    s3Uri: (value: string) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: EvaluationDatasetLocation, visitor: Visitor<T>) => T;
}
export interface EvaluationDataset {
  name: string | undefined;
  datasetLocation?: EvaluationDatasetLocation | undefined;
}
export declare const EvaluationTaskType: {
  readonly CLASSIFICATION: "Classification";
  readonly CUSTOM: "Custom";
  readonly GENERATION: "Generation";
  readonly QUESTION_AND_ANSWER: "QuestionAndAnswer";
  readonly SUMMARIZATION: "Summarization";
};
export type EvaluationTaskType =
  (typeof EvaluationTaskType)[keyof typeof EvaluationTaskType];
export interface EvaluationDatasetMetricConfig {
  taskType: EvaluationTaskType | undefined;
  dataset: EvaluationDataset | undefined;
  metricNames: string[] | undefined;
}
export interface BedrockEvaluatorModel {
  modelIdentifier: string | undefined;
}
export type EvaluatorModelConfig =
  | EvaluatorModelConfig.BedrockEvaluatorModelsMember
  | EvaluatorModelConfig.$UnknownMember;
export declare namespace EvaluatorModelConfig {
  interface BedrockEvaluatorModelsMember {
    bedrockEvaluatorModels: BedrockEvaluatorModel[];
    $unknown?: never;
  }
  interface $UnknownMember {
    bedrockEvaluatorModels?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    bedrockEvaluatorModels: (value: BedrockEvaluatorModel[]) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: EvaluatorModelConfig, visitor: Visitor<T>) => T;
}
export interface AutomatedEvaluationConfig {
  datasetMetricConfigs: EvaluationDatasetMetricConfig[] | undefined;
  evaluatorModelConfig?: EvaluatorModelConfig | undefined;
  customMetricConfig?: AutomatedEvaluationCustomMetricConfig | undefined;
}
export interface HumanEvaluationCustomMetric {
  name: string | undefined;
  description?: string | undefined;
  ratingMethod: string | undefined;
}
export interface HumanWorkflowConfig {
  flowDefinitionArn: string | undefined;
  instructions?: string | undefined;
}
export interface HumanEvaluationConfig {
  humanWorkflowConfig?: HumanWorkflowConfig | undefined;
  customMetrics?: HumanEvaluationCustomMetric[] | undefined;
  datasetMetricConfigs: EvaluationDatasetMetricConfig[] | undefined;
}
export type EvaluationConfig =
  | EvaluationConfig.AutomatedMember
  | EvaluationConfig.HumanMember
  | EvaluationConfig.$UnknownMember;
export declare namespace EvaluationConfig {
  interface AutomatedMember {
    automated: AutomatedEvaluationConfig;
    human?: never;
    $unknown?: never;
  }
  interface HumanMember {
    automated?: never;
    human: HumanEvaluationConfig;
    $unknown?: never;
  }
  interface $UnknownMember {
    automated?: never;
    human?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    automated: (value: AutomatedEvaluationConfig) => T;
    human: (value: HumanEvaluationConfig) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: EvaluationConfig, visitor: Visitor<T>) => T;
}
export declare const PerformanceConfigLatency: {
  readonly OPTIMIZED: "optimized";
  readonly STANDARD: "standard";
};
export type PerformanceConfigLatency =
  (typeof PerformanceConfigLatency)[keyof typeof PerformanceConfigLatency];
export interface PerformanceConfiguration {
  latency?: PerformanceConfigLatency | undefined;
}
export interface EvaluationBedrockModel {
  modelIdentifier: string | undefined;
  inferenceParams?: string | undefined;
  performanceConfig?: PerformanceConfiguration | undefined;
}
export interface EvaluationPrecomputedInferenceSource {
  inferenceSourceIdentifier: string | undefined;
}
export type EvaluationModelConfig =
  | EvaluationModelConfig.BedrockModelMember
  | EvaluationModelConfig.PrecomputedInferenceSourceMember
  | EvaluationModelConfig.$UnknownMember;
export declare namespace EvaluationModelConfig {
  interface BedrockModelMember {
    bedrockModel: EvaluationBedrockModel;
    precomputedInferenceSource?: never;
    $unknown?: never;
  }
  interface PrecomputedInferenceSourceMember {
    bedrockModel?: never;
    precomputedInferenceSource: EvaluationPrecomputedInferenceSource;
    $unknown?: never;
  }
  interface $UnknownMember {
    bedrockModel?: never;
    precomputedInferenceSource?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    bedrockModel: (value: EvaluationBedrockModel) => T;
    precomputedInferenceSource: (
      value: EvaluationPrecomputedInferenceSource
    ) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: EvaluationModelConfig, visitor: Visitor<T>) => T;
}
export interface GuardrailConfiguration {
  guardrailId: string | undefined;
  guardrailVersion: string | undefined;
}
export interface TextInferenceConfig {
  temperature?: number | undefined;
  topP?: number | undefined;
  maxTokens?: number | undefined;
  stopSequences?: string[] | undefined;
}
export interface KbInferenceConfig {
  textInferenceConfig?: TextInferenceConfig | undefined;
}
export interface PromptTemplate {
  textPromptTemplate?: string | undefined;
}
export interface ExternalSourcesGenerationConfiguration {
  promptTemplate?: PromptTemplate | undefined;
  guardrailConfiguration?: GuardrailConfiguration | undefined;
  kbInferenceConfig?: KbInferenceConfig | undefined;
  additionalModelRequestFields?: Record<string, __DocumentType> | undefined;
}
export interface ByteContentDoc {
  identifier: string | undefined;
  contentType: string | undefined;
  data: Uint8Array | undefined;
}
export interface S3ObjectDoc {
  uri: string | undefined;
}
export declare const ExternalSourceType: {
  readonly BYTE_CONTENT: "BYTE_CONTENT";
  readonly S3: "S3";
};
export type ExternalSourceType =
  (typeof ExternalSourceType)[keyof typeof ExternalSourceType];
export interface ExternalSource {
  sourceType: ExternalSourceType | undefined;
  s3Location?: S3ObjectDoc | undefined;
  byteContent?: ByteContentDoc | undefined;
}
export interface ExternalSourcesRetrieveAndGenerateConfiguration {
  modelArn: string | undefined;
  sources: ExternalSource[] | undefined;
  generationConfiguration?: ExternalSourcesGenerationConfiguration | undefined;
}
export interface GenerationConfiguration {
  promptTemplate?: PromptTemplate | undefined;
  guardrailConfiguration?: GuardrailConfiguration | undefined;
  kbInferenceConfig?: KbInferenceConfig | undefined;
  additionalModelRequestFields?: Record<string, __DocumentType> | undefined;
}
export declare const QueryTransformationType: {
  readonly QUERY_DECOMPOSITION: "QUERY_DECOMPOSITION";
};
export type QueryTransformationType =
  (typeof QueryTransformationType)[keyof typeof QueryTransformationType];
export interface QueryTransformationConfiguration {
  type: QueryTransformationType | undefined;
}
export interface OrchestrationConfiguration {
  queryTransformationConfiguration:
    | QueryTransformationConfiguration
    | undefined;
}
export interface FilterAttribute {
  key: string | undefined;
  value: __DocumentType | undefined;
}
export declare const AttributeType: {
  readonly BOOLEAN: "BOOLEAN";
  readonly NUMBER: "NUMBER";
  readonly STRING: "STRING";
  readonly STRING_LIST: "STRING_LIST";
};
export type AttributeType = (typeof AttributeType)[keyof typeof AttributeType];
export interface MetadataAttributeSchema {
  key: string | undefined;
  type: AttributeType | undefined;
  description: string | undefined;
}
export interface ImplicitFilterConfiguration {
  metadataAttributes: MetadataAttributeSchema[] | undefined;
  modelArn: string | undefined;
}
export declare const SearchType: {
  readonly HYBRID: "HYBRID";
  readonly SEMANTIC: "SEMANTIC";
};
export type SearchType = (typeof SearchType)[keyof typeof SearchType];
export declare const RerankingMetadataSelectionMode: {
  readonly ALL: "ALL";
  readonly SELECTIVE: "SELECTIVE";
};
export type RerankingMetadataSelectionMode =
  (typeof RerankingMetadataSelectionMode)[keyof typeof RerankingMetadataSelectionMode];
export interface FieldForReranking {
  fieldName: string | undefined;
}
export type RerankingMetadataSelectiveModeConfiguration =
  | RerankingMetadataSelectiveModeConfiguration.FieldsToExcludeMember
  | RerankingMetadataSelectiveModeConfiguration.FieldsToIncludeMember
  | RerankingMetadataSelectiveModeConfiguration.$UnknownMember;
export declare namespace RerankingMetadataSelectiveModeConfiguration {
  interface FieldsToIncludeMember {
    fieldsToInclude: FieldForReranking[];
    fieldsToExclude?: never;
    $unknown?: never;
  }
  interface FieldsToExcludeMember {
    fieldsToInclude?: never;
    fieldsToExclude: FieldForReranking[];
    $unknown?: never;
  }
  interface $UnknownMember {
    fieldsToInclude?: never;
    fieldsToExclude?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    fieldsToInclude: (value: FieldForReranking[]) => T;
    fieldsToExclude: (value: FieldForReranking[]) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(
    value: RerankingMetadataSelectiveModeConfiguration,
    visitor: Visitor<T>
  ) => T;
}
export interface MetadataConfigurationForReranking {
  selectionMode: RerankingMetadataSelectionMode | undefined;
  selectiveModeConfiguration?:
    | RerankingMetadataSelectiveModeConfiguration
    | undefined;
}
export interface VectorSearchBedrockRerankingModelConfiguration {
  modelArn: string | undefined;
  additionalModelRequestFields?: Record<string, __DocumentType> | undefined;
}
export interface VectorSearchBedrockRerankingConfiguration {
  modelConfiguration:
    | VectorSearchBedrockRerankingModelConfiguration
    | undefined;
  numberOfRerankedResults?: number | undefined;
  metadataConfiguration?: MetadataConfigurationForReranking | undefined;
}
export declare const VectorSearchRerankingConfigurationType: {
  readonly BEDROCK_RERANKING_MODEL: "BEDROCK_RERANKING_MODEL";
};
export type VectorSearchRerankingConfigurationType =
  (typeof VectorSearchRerankingConfigurationType)[keyof typeof VectorSearchRerankingConfigurationType];
export interface VectorSearchRerankingConfiguration {
  type: VectorSearchRerankingConfigurationType | undefined;
  bedrockRerankingConfiguration?:
    | VectorSearchBedrockRerankingConfiguration
    | undefined;
}
export declare const RetrieveAndGenerateType: {
  readonly EXTERNAL_SOURCES: "EXTERNAL_SOURCES";
  readonly KNOWLEDGE_BASE: "KNOWLEDGE_BASE";
};
export type RetrieveAndGenerateType =
  (typeof RetrieveAndGenerateType)[keyof typeof RetrieveAndGenerateType];
export interface EvaluationPrecomputedRetrieveAndGenerateSourceConfig {
  ragSourceIdentifier: string | undefined;
}
export interface EvaluationPrecomputedRetrieveSourceConfig {
  ragSourceIdentifier: string | undefined;
}
export type EvaluationPrecomputedRagSourceConfig =
  | EvaluationPrecomputedRagSourceConfig.RetrieveAndGenerateSourceConfigMember
  | EvaluationPrecomputedRagSourceConfig.RetrieveSourceConfigMember
  | EvaluationPrecomputedRagSourceConfig.$UnknownMember;
export declare namespace EvaluationPrecomputedRagSourceConfig {
  interface RetrieveSourceConfigMember {
    retrieveSourceConfig: EvaluationPrecomputedRetrieveSourceConfig;
    retrieveAndGenerateSourceConfig?: never;
    $unknown?: never;
  }
  interface RetrieveAndGenerateSourceConfigMember {
    retrieveSourceConfig?: never;
    retrieveAndGenerateSourceConfig: EvaluationPrecomputedRetrieveAndGenerateSourceConfig;
    $unknown?: never;
  }
  interface $UnknownMember {
    retrieveSourceConfig?: never;
    retrieveAndGenerateSourceConfig?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    retrieveSourceConfig: (
      value: EvaluationPrecomputedRetrieveSourceConfig
    ) => T;
    retrieveAndGenerateSourceConfig: (
      value: EvaluationPrecomputedRetrieveAndGenerateSourceConfig
    ) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(
    value: EvaluationPrecomputedRagSourceConfig,
    visitor: Visitor<T>
  ) => T;
}
export interface EvaluationOutputDataConfig {
  s3Uri: string | undefined;
}
export interface CreateEvaluationJobResponse {
  jobArn: string | undefined;
}
export interface GetEvaluationJobRequest {
  jobIdentifier: string | undefined;
}
export declare const EvaluationJobType: {
  readonly AUTOMATED: "Automated";
  readonly HUMAN: "Human";
};
export type EvaluationJobType =
  (typeof EvaluationJobType)[keyof typeof EvaluationJobType];
export declare const SortJobsBy: {
  readonly CREATION_TIME: "CreationTime";
};
export type SortJobsBy = (typeof SortJobsBy)[keyof typeof SortJobsBy];
export interface ListEvaluationJobsRequest {
  creationTimeAfter?: Date | undefined;
  creationTimeBefore?: Date | undefined;
  statusEquals?: EvaluationJobStatus | undefined;
  applicationTypeEquals?: ApplicationType | undefined;
  nameContains?: string | undefined;
  maxResults?: number | undefined;
  nextToken?: string | undefined;
  sortBy?: SortJobsBy | undefined;
  sortOrder?: SortOrder | undefined;
}
export interface EvaluationModelConfigSummary {
  bedrockModelIdentifiers?: string[] | undefined;
  precomputedInferenceSourceIdentifiers?: string[] | undefined;
}
export interface EvaluationRagConfigSummary {
  bedrockKnowledgeBaseIdentifiers?: string[] | undefined;
  precomputedRagSourceIdentifiers?: string[] | undefined;
}
export interface EvaluationInferenceConfigSummary {
  modelConfigSummary?: EvaluationModelConfigSummary | undefined;
  ragConfigSummary?: EvaluationRagConfigSummary | undefined;
}
export interface EvaluationSummary {
  jobArn: string | undefined;
  jobName: string | undefined;
  status: EvaluationJobStatus | undefined;
  creationTime: Date | undefined;
  jobType: EvaluationJobType | undefined;
  evaluationTaskTypes: EvaluationTaskType[] | undefined;
  modelIdentifiers?: string[] | undefined;
  ragIdentifiers?: string[] | undefined;
  evaluatorModelIdentifiers?: string[] | undefined;
  customMetricsEvaluatorModelIdentifiers?: string[] | undefined;
  inferenceConfigSummary?: EvaluationInferenceConfigSummary | undefined;
  applicationType?: ApplicationType | undefined;
}
export interface ListEvaluationJobsResponse {
  nextToken?: string | undefined;
  jobSummaries?: EvaluationSummary[] | undefined;
}
export interface StopEvaluationJobRequest {
  jobIdentifier: string | undefined;
}
export interface StopEvaluationJobResponse {}
export interface GuardrailAutomatedReasoningPolicyConfig {
  policies: string[] | undefined;
  confidenceThreshold?: number | undefined;
}
export declare const GuardrailContentFilterAction: {
  readonly BLOCK: "BLOCK";
  readonly NONE: "NONE";
};
export type GuardrailContentFilterAction =
  (typeof GuardrailContentFilterAction)[keyof typeof GuardrailContentFilterAction];
export declare const GuardrailModality: {
  readonly IMAGE: "IMAGE";
  readonly TEXT: "TEXT";
};
export type GuardrailModality =
  (typeof GuardrailModality)[keyof typeof GuardrailModality];
export declare const GuardrailFilterStrength: {
  readonly HIGH: "HIGH";
  readonly LOW: "LOW";
  readonly MEDIUM: "MEDIUM";
  readonly NONE: "NONE";
};
export type GuardrailFilterStrength =
  (typeof GuardrailFilterStrength)[keyof typeof GuardrailFilterStrength];
export declare const GuardrailContentFilterType: {
  readonly HATE: "HATE";
  readonly INSULTS: "INSULTS";
  readonly MISCONDUCT: "MISCONDUCT";
  readonly PROMPT_ATTACK: "PROMPT_ATTACK";
  readonly SEXUAL: "SEXUAL";
  readonly VIOLENCE: "VIOLENCE";
};
export type GuardrailContentFilterType =
  (typeof GuardrailContentFilterType)[keyof typeof GuardrailContentFilterType];
export interface GuardrailContentFilterConfig {
  type: GuardrailContentFilterType | undefined;
  inputStrength: GuardrailFilterStrength | undefined;
  outputStrength: GuardrailFilterStrength | undefined;
  inputModalities?: GuardrailModality[] | undefined;
  outputModalities?: GuardrailModality[] | undefined;
  inputAction?: GuardrailContentFilterAction | undefined;
  outputAction?: GuardrailContentFilterAction | undefined;
  inputEnabled?: boolean | undefined;
  outputEnabled?: boolean | undefined;
}
export declare const GuardrailContentFiltersTierName: {
  readonly CLASSIC: "CLASSIC";
  readonly STANDARD: "STANDARD";
};
export type GuardrailContentFiltersTierName =
  (typeof GuardrailContentFiltersTierName)[keyof typeof GuardrailContentFiltersTierName];
export interface GuardrailContentFiltersTierConfig {
  tierName: GuardrailContentFiltersTierName | undefined;
}
export interface GuardrailContentPolicyConfig {
  filtersConfig: GuardrailContentFilterConfig[] | undefined;
  tierConfig?: GuardrailContentFiltersTierConfig | undefined;
}
export declare const GuardrailContextualGroundingAction: {
  readonly BLOCK: "BLOCK";
  readonly NONE: "NONE";
};
export type GuardrailContextualGroundingAction =
  (typeof GuardrailContextualGroundingAction)[keyof typeof GuardrailContextualGroundingAction];
export declare const GuardrailContextualGroundingFilterType: {
  readonly GROUNDING: "GROUNDING";
  readonly RELEVANCE: "RELEVANCE";
};
export type GuardrailContextualGroundingFilterType =
  (typeof GuardrailContextualGroundingFilterType)[keyof typeof GuardrailContextualGroundingFilterType];
export interface GuardrailContextualGroundingFilterConfig {
  type: GuardrailContextualGroundingFilterType | undefined;
  threshold: number | undefined;
  action?: GuardrailContextualGroundingAction | undefined;
  enabled?: boolean | undefined;
}
export interface GuardrailContextualGroundingPolicyConfig {
  filtersConfig: GuardrailContextualGroundingFilterConfig[] | undefined;
}
export interface GuardrailCrossRegionConfig {
  guardrailProfileIdentifier: string | undefined;
}
export declare const GuardrailSensitiveInformationAction: {
  readonly ANONYMIZE: "ANONYMIZE";
  readonly BLOCK: "BLOCK";
  readonly NONE: "NONE";
};
export type GuardrailSensitiveInformationAction =
  (typeof GuardrailSensitiveInformationAction)[keyof typeof GuardrailSensitiveInformationAction];
export declare const GuardrailPiiEntityType: {
  readonly ADDRESS: "ADDRESS";
  readonly AGE: "AGE";
  readonly AWS_ACCESS_KEY: "AWS_ACCESS_KEY";
  readonly AWS_SECRET_KEY: "AWS_SECRET_KEY";
  readonly CA_HEALTH_NUMBER: "CA_HEALTH_NUMBER";
  readonly CA_SOCIAL_INSURANCE_NUMBER: "CA_SOCIAL_INSURANCE_NUMBER";
  readonly CREDIT_DEBIT_CARD_CVV: "CREDIT_DEBIT_CARD_CVV";
  readonly CREDIT_DEBIT_CARD_EXPIRY: "CREDIT_DEBIT_CARD_EXPIRY";
  readonly CREDIT_DEBIT_CARD_NUMBER: "CREDIT_DEBIT_CARD_NUMBER";
  readonly DRIVER_ID: "DRIVER_ID";
  readonly EMAIL: "EMAIL";
  readonly INTERNATIONAL_BANK_ACCOUNT_NUMBER: "INTERNATIONAL_BANK_ACCOUNT_NUMBER";
  readonly IP_ADDRESS: "IP_ADDRESS";
  readonly LICENSE_PLATE: "LICENSE_PLATE";
  readonly MAC_ADDRESS: "MAC_ADDRESS";
  readonly NAME: "NAME";
  readonly PASSWORD: "PASSWORD";
  readonly PHONE: "PHONE";
  readonly PIN: "PIN";
  readonly SWIFT_CODE: "SWIFT_CODE";
  readonly UK_NATIONAL_HEALTH_SERVICE_NUMBER: "UK_NATIONAL_HEALTH_SERVICE_NUMBER";
  readonly UK_NATIONAL_INSURANCE_NUMBER: "UK_NATIONAL_INSURANCE_NUMBER";
  readonly UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER: "UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER";
  readonly URL: "URL";
  readonly USERNAME: "USERNAME";
  readonly US_BANK_ACCOUNT_NUMBER: "US_BANK_ACCOUNT_NUMBER";
  readonly US_BANK_ROUTING_NUMBER: "US_BANK_ROUTING_NUMBER";
  readonly US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER: "US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER";
  readonly US_PASSPORT_NUMBER: "US_PASSPORT_NUMBER";
  readonly US_SOCIAL_SECURITY_NUMBER: "US_SOCIAL_SECURITY_NUMBER";
  readonly VEHICLE_IDENTIFICATION_NUMBER: "VEHICLE_IDENTIFICATION_NUMBER";
};
export type GuardrailPiiEntityType =
  (typeof GuardrailPiiEntityType)[keyof typeof GuardrailPiiEntityType];
export interface GuardrailPiiEntityConfig {
  type: GuardrailPiiEntityType | undefined;
  action: GuardrailSensitiveInformationAction | undefined;
  inputAction?: GuardrailSensitiveInformationAction | undefined;
  outputAction?: GuardrailSensitiveInformationAction | undefined;
  inputEnabled?: boolean | undefined;
  outputEnabled?: boolean | undefined;
}
export interface GuardrailRegexConfig {
  name: string | undefined;
  description?: string | undefined;
  pattern: string | undefined;
  action: GuardrailSensitiveInformationAction | undefined;
  inputAction?: GuardrailSensitiveInformationAction | undefined;
  outputAction?: GuardrailSensitiveInformationAction | undefined;
  inputEnabled?: boolean | undefined;
  outputEnabled?: boolean | undefined;
}
export interface GuardrailSensitiveInformationPolicyConfig {
  piiEntitiesConfig?: GuardrailPiiEntityConfig[] | undefined;
  regexesConfig?: GuardrailRegexConfig[] | undefined;
}
export declare const GuardrailTopicsTierName: {
  readonly CLASSIC: "CLASSIC";
  readonly STANDARD: "STANDARD";
};
export type GuardrailTopicsTierName =
  (typeof GuardrailTopicsTierName)[keyof typeof GuardrailTopicsTierName];
export interface GuardrailTopicsTierConfig {
  tierName: GuardrailTopicsTierName | undefined;
}
export declare const GuardrailTopicAction: {
  readonly BLOCK: "BLOCK";
  readonly NONE: "NONE";
};
export type GuardrailTopicAction =
  (typeof GuardrailTopicAction)[keyof typeof GuardrailTopicAction];
export declare const GuardrailTopicType: {
  readonly DENY: "DENY";
};
export type GuardrailTopicType =
  (typeof GuardrailTopicType)[keyof typeof GuardrailTopicType];
export interface GuardrailTopicConfig {
  name: string | undefined;
  definition: string | undefined;
  examples?: string[] | undefined;
  type: GuardrailTopicType | undefined;
  inputAction?: GuardrailTopicAction | undefined;
  outputAction?: GuardrailTopicAction | undefined;
  inputEnabled?: boolean | undefined;
  outputEnabled?: boolean | undefined;
}
export interface GuardrailTopicPolicyConfig {
  topicsConfig: GuardrailTopicConfig[] | undefined;
  tierConfig?: GuardrailTopicsTierConfig | undefined;
}
export declare const GuardrailWordAction: {
  readonly BLOCK: "BLOCK";
  readonly NONE: "NONE";
};
export type GuardrailWordAction =
  (typeof GuardrailWordAction)[keyof typeof GuardrailWordAction];
export declare const AutomatedReasoningPolicyDefinitionRuleFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyDefinitionRule
) => any;
export declare const AutomatedReasoningPolicyDefinitionTypeValueFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyDefinitionTypeValue
) => any;
export declare const AutomatedReasoningPolicyDefinitionTypeFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyDefinitionType
) => any;
export declare const AutomatedReasoningPolicyDefinitionVariableFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyDefinitionVariable
) => any;
export declare const AutomatedReasoningPolicyDefinitionFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyDefinition
) => any;
export declare const CreateAutomatedReasoningPolicyRequestFilterSensitiveLog: (
  obj: CreateAutomatedReasoningPolicyRequest
) => any;
export declare const CreateAutomatedReasoningPolicyResponseFilterSensitiveLog: (
  obj: CreateAutomatedReasoningPolicyResponse
) => any;
export declare const CreateAutomatedReasoningPolicyTestCaseRequestFilterSensitiveLog: (
  obj: CreateAutomatedReasoningPolicyTestCaseRequest
) => any;
export declare const CreateAutomatedReasoningPolicyVersionResponseFilterSensitiveLog: (
  obj: CreateAutomatedReasoningPolicyVersionResponse
) => any;
export declare const ExportAutomatedReasoningPolicyVersionResponseFilterSensitiveLog: (
  obj: ExportAutomatedReasoningPolicyVersionResponse
) => any;
export declare const GetAutomatedReasoningPolicyResponseFilterSensitiveLog: (
  obj: GetAutomatedReasoningPolicyResponse
) => any;
export declare const AutomatedReasoningPolicyAddRuleAnnotationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyAddRuleAnnotation
) => any;
export declare const AutomatedReasoningPolicyAddRuleFromNaturalLanguageAnnotationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyAddRuleFromNaturalLanguageAnnotation
) => any;
export declare const AutomatedReasoningPolicyAddTypeAnnotationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyAddTypeAnnotation
) => any;
export declare const AutomatedReasoningPolicyAddVariableAnnotationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyAddVariableAnnotation
) => any;
export declare const AutomatedReasoningPolicyDeleteTypeAnnotationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyDeleteTypeAnnotation
) => any;
export declare const AutomatedReasoningPolicyDeleteVariableAnnotationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyDeleteVariableAnnotation
) => any;
export declare const AutomatedReasoningPolicyIngestContentAnnotationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyIngestContentAnnotation
) => any;
export declare const AutomatedReasoningPolicyUpdateFromRuleFeedbackAnnotationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyUpdateFromRuleFeedbackAnnotation
) => any;
export declare const AutomatedReasoningPolicyUpdateFromScenarioFeedbackAnnotationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyUpdateFromScenarioFeedbackAnnotation
) => any;
export declare const AutomatedReasoningPolicyUpdateRuleAnnotationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyUpdateRuleAnnotation
) => any;
export declare const AutomatedReasoningPolicyAddTypeValueFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyAddTypeValue
) => any;
export declare const AutomatedReasoningPolicyUpdateTypeValueFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyUpdateTypeValue
) => any;
export declare const AutomatedReasoningPolicyTypeValueAnnotationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyTypeValueAnnotation
) => any;
export declare const AutomatedReasoningPolicyUpdateTypeAnnotationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyUpdateTypeAnnotation
) => any;
export declare const AutomatedReasoningPolicyUpdateVariableAnnotationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyUpdateVariableAnnotation
) => any;
export declare const AutomatedReasoningPolicyAnnotationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyAnnotation
) => any;
export declare const GetAutomatedReasoningPolicyAnnotationsResponseFilterSensitiveLog: (
  obj: GetAutomatedReasoningPolicyAnnotationsResponse
) => any;
export declare const GetAutomatedReasoningPolicyBuildWorkflowResponseFilterSensitiveLog: (
  obj: GetAutomatedReasoningPolicyBuildWorkflowResponse
) => any;
export declare const AutomatedReasoningPolicyAddRuleMutationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyAddRuleMutation
) => any;
export declare const AutomatedReasoningPolicyAddTypeMutationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyAddTypeMutation
) => any;
export declare const AutomatedReasoningPolicyAddVariableMutationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyAddVariableMutation
) => any;
export declare const AutomatedReasoningPolicyDeleteTypeMutationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyDeleteTypeMutation
) => any;
export declare const AutomatedReasoningPolicyDeleteVariableMutationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyDeleteVariableMutation
) => any;
export declare const AutomatedReasoningPolicyUpdateRuleMutationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyUpdateRuleMutation
) => any;
export declare const AutomatedReasoningPolicyUpdateTypeMutationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyUpdateTypeMutation
) => any;
export declare const AutomatedReasoningPolicyUpdateVariableMutationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyUpdateVariableMutation
) => any;
export declare const AutomatedReasoningPolicyMutationFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyMutation
) => any;
export declare const AutomatedReasoningPolicyBuildStepContextFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyBuildStepContext
) => any;
export declare const AutomatedReasoningPolicyDefinitionElementFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyDefinitionElement
) => any;
export declare const AutomatedReasoningPolicyBuildStepFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyBuildStep
) => any;
export declare const AutomatedReasoningPolicyBuildLogEntryFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyBuildLogEntry
) => any;
export declare const AutomatedReasoningPolicyBuildLogFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyBuildLog
) => any;
export declare const AutomatedReasoningPolicyDisjointRuleSetFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyDisjointRuleSet
) => any;
export declare const AutomatedReasoningPolicyDefinitionTypeValuePairFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyDefinitionTypeValuePair
) => any;
export declare const AutomatedReasoningPolicyDefinitionQualityReportFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyDefinitionQualityReport
) => any;
export declare const AutomatedReasoningPolicyBuildResultAssetsFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyBuildResultAssets
) => any;
export declare const GetAutomatedReasoningPolicyBuildWorkflowResultAssetsResponseFilterSensitiveLog: (
  obj: GetAutomatedReasoningPolicyBuildWorkflowResultAssetsResponse
) => any;
export declare const AutomatedReasoningPolicyScenarioFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyScenario
) => any;
export declare const GetAutomatedReasoningPolicyNextScenarioResponseFilterSensitiveLog: (
  obj: GetAutomatedReasoningPolicyNextScenarioResponse
) => any;
export declare const AutomatedReasoningPolicyTestCaseFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyTestCase
) => any;
export declare const GetAutomatedReasoningPolicyTestCaseResponseFilterSensitiveLog: (
  obj: GetAutomatedReasoningPolicyTestCaseResponse
) => any;
export declare const AutomatedReasoningLogicStatementFilterSensitiveLog: (
  obj: AutomatedReasoningLogicStatement
) => any;
export declare const AutomatedReasoningCheckLogicWarningFilterSensitiveLog: (
  obj: AutomatedReasoningCheckLogicWarning
) => any;
export declare const AutomatedReasoningCheckInputTextReferenceFilterSensitiveLog: (
  obj: AutomatedReasoningCheckInputTextReference
) => any;
export declare const AutomatedReasoningCheckTranslationFilterSensitiveLog: (
  obj: AutomatedReasoningCheckTranslation
) => any;
export declare const AutomatedReasoningCheckImpossibleFindingFilterSensitiveLog: (
  obj: AutomatedReasoningCheckImpossibleFinding
) => any;
export declare const AutomatedReasoningCheckInvalidFindingFilterSensitiveLog: (
  obj: AutomatedReasoningCheckInvalidFinding
) => any;
export declare const AutomatedReasoningCheckScenarioFilterSensitiveLog: (
  obj: AutomatedReasoningCheckScenario
) => any;
export declare const AutomatedReasoningCheckSatisfiableFindingFilterSensitiveLog: (
  obj: AutomatedReasoningCheckSatisfiableFinding
) => any;
export declare const AutomatedReasoningCheckTranslationOptionFilterSensitiveLog: (
  obj: AutomatedReasoningCheckTranslationOption
) => any;
export declare const AutomatedReasoningCheckTranslationAmbiguousFindingFilterSensitiveLog: (
  obj: AutomatedReasoningCheckTranslationAmbiguousFinding
) => any;
export declare const AutomatedReasoningCheckValidFindingFilterSensitiveLog: (
  obj: AutomatedReasoningCheckValidFinding
) => any;
export declare const AutomatedReasoningCheckFindingFilterSensitiveLog: (
  obj: AutomatedReasoningCheckFinding
) => any;
export declare const AutomatedReasoningPolicyTestResultFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyTestResult
) => any;
export declare const GetAutomatedReasoningPolicyTestResultResponseFilterSensitiveLog: (
  obj: GetAutomatedReasoningPolicyTestResultResponse
) => any;
export declare const AutomatedReasoningPolicySummaryFilterSensitiveLog: (
  obj: AutomatedReasoningPolicySummary
) => any;
export declare const ListAutomatedReasoningPoliciesResponseFilterSensitiveLog: (
  obj: ListAutomatedReasoningPoliciesResponse
) => any;
export declare const ListAutomatedReasoningPolicyTestCasesResponseFilterSensitiveLog: (
  obj: ListAutomatedReasoningPolicyTestCasesResponse
) => any;
export declare const ListAutomatedReasoningPolicyTestResultsResponseFilterSensitiveLog: (
  obj: ListAutomatedReasoningPolicyTestResultsResponse
) => any;
export declare const AutomatedReasoningPolicyBuildWorkflowDocumentFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyBuildWorkflowDocument
) => any;
export declare const AutomatedReasoningPolicyBuildWorkflowRepairContentFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyBuildWorkflowRepairContent
) => any;
export declare const AutomatedReasoningPolicyWorkflowTypeContentFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyWorkflowTypeContent
) => any;
export declare const AutomatedReasoningPolicyBuildWorkflowSourceFilterSensitiveLog: (
  obj: AutomatedReasoningPolicyBuildWorkflowSource
) => any;
export declare const StartAutomatedReasoningPolicyBuildWorkflowRequestFilterSensitiveLog: (
  obj: StartAutomatedReasoningPolicyBuildWorkflowRequest
) => any;
export declare const UpdateAutomatedReasoningPolicyRequestFilterSensitiveLog: (
  obj: UpdateAutomatedReasoningPolicyRequest
) => any;
export declare const UpdateAutomatedReasoningPolicyResponseFilterSensitiveLog: (
  obj: UpdateAutomatedReasoningPolicyResponse
) => any;
export declare const UpdateAutomatedReasoningPolicyAnnotationsRequestFilterSensitiveLog: (
  obj: UpdateAutomatedReasoningPolicyAnnotationsRequest
) => any;
export declare const UpdateAutomatedReasoningPolicyTestCaseRequestFilterSensitiveLog: (
  obj: UpdateAutomatedReasoningPolicyTestCaseRequest
) => any;
export declare const RequestMetadataBaseFiltersFilterSensitiveLog: (
  obj: RequestMetadataBaseFilters
) => any;
export declare const RequestMetadataFiltersFilterSensitiveLog: (
  obj: RequestMetadataFilters
) => any;
export declare const InvocationLogsConfigFilterSensitiveLog: (
  obj: InvocationLogsConfig
) => any;
export declare const TrainingDataConfigFilterSensitiveLog: (
  obj: TrainingDataConfig
) => any;
export declare const GetCustomModelResponseFilterSensitiveLog: (
  obj: GetCustomModelResponse
) => any;
export declare const BatchDeleteEvaluationJobRequestFilterSensitiveLog: (
  obj: BatchDeleteEvaluationJobRequest
) => any;
export declare const BatchDeleteEvaluationJobErrorFilterSensitiveLog: (
  obj: BatchDeleteEvaluationJobError
) => any;
export declare const BatchDeleteEvaluationJobItemFilterSensitiveLog: (
  obj: BatchDeleteEvaluationJobItem
) => any;
export declare const BatchDeleteEvaluationJobResponseFilterSensitiveLog: (
  obj: BatchDeleteEvaluationJobResponse
) => any;
export declare const CustomMetricDefinitionFilterSensitiveLog: (
  obj: CustomMetricDefinition
) => any;
export declare const AutomatedEvaluationCustomMetricSourceFilterSensitiveLog: (
  obj: AutomatedEvaluationCustomMetricSource
) => any;
export declare const AutomatedEvaluationCustomMetricConfigFilterSensitiveLog: (
  obj: AutomatedEvaluationCustomMetricConfig
) => any;
export declare const EvaluationDatasetFilterSensitiveLog: (
  obj: EvaluationDataset
) => any;
export declare const EvaluationDatasetMetricConfigFilterSensitiveLog: (
  obj: EvaluationDatasetMetricConfig
) => any;
export declare const AutomatedEvaluationConfigFilterSensitiveLog: (
  obj: AutomatedEvaluationConfig
) => any;
export declare const HumanEvaluationCustomMetricFilterSensitiveLog: (
  obj: HumanEvaluationCustomMetric
) => any;
export declare const HumanWorkflowConfigFilterSensitiveLog: (
  obj: HumanWorkflowConfig
) => any;
export declare const HumanEvaluationConfigFilterSensitiveLog: (
  obj: HumanEvaluationConfig
) => any;
export declare const EvaluationConfigFilterSensitiveLog: (
  obj: EvaluationConfig
) => any;
export declare const EvaluationBedrockModelFilterSensitiveLog: (
  obj: EvaluationBedrockModel
) => any;
export declare const EvaluationModelConfigFilterSensitiveLog: (
  obj: EvaluationModelConfig
) => any;
export declare const PromptTemplateFilterSensitiveLog: (
  obj: PromptTemplate
) => any;
export declare const ExternalSourcesGenerationConfigurationFilterSensitiveLog: (
  obj: ExternalSourcesGenerationConfiguration
) => any;
export declare const ByteContentDocFilterSensitiveLog: (
  obj: ByteContentDoc
) => any;
export declare const ExternalSourceFilterSensitiveLog: (
  obj: ExternalSource
) => any;
export declare const ExternalSourcesRetrieveAndGenerateConfigurationFilterSensitiveLog: (
  obj: ExternalSourcesRetrieveAndGenerateConfiguration
) => any;
export declare const GenerationConfigurationFilterSensitiveLog: (
  obj: GenerationConfiguration
) => any;
export declare const MetadataAttributeSchemaFilterSensitiveLog: (
  obj: MetadataAttributeSchema
) => any;
export declare const ImplicitFilterConfigurationFilterSensitiveLog: (
  obj: ImplicitFilterConfiguration
) => any;
export declare const RerankingMetadataSelectiveModeConfigurationFilterSensitiveLog: (
  obj: RerankingMetadataSelectiveModeConfiguration
) => any;
export declare const MetadataConfigurationForRerankingFilterSensitiveLog: (
  obj: MetadataConfigurationForReranking
) => any;
export declare const VectorSearchBedrockRerankingConfigurationFilterSensitiveLog: (
  obj: VectorSearchBedrockRerankingConfiguration
) => any;
export declare const VectorSearchRerankingConfigurationFilterSensitiveLog: (
  obj: VectorSearchRerankingConfiguration
) => any;
export declare const GetEvaluationJobRequestFilterSensitiveLog: (
  obj: GetEvaluationJobRequest
) => any;
export declare const StopEvaluationJobRequestFilterSensitiveLog: (
  obj: StopEvaluationJobRequest
) => any;
export declare const GuardrailContentFilterConfigFilterSensitiveLog: (
  obj: GuardrailContentFilterConfig
) => any;
export declare const GuardrailContentFiltersTierConfigFilterSensitiveLog: (
  obj: GuardrailContentFiltersTierConfig
) => any;
export declare const GuardrailContentPolicyConfigFilterSensitiveLog: (
  obj: GuardrailContentPolicyConfig
) => any;
export declare const GuardrailContextualGroundingFilterConfigFilterSensitiveLog: (
  obj: GuardrailContextualGroundingFilterConfig
) => any;
export declare const GuardrailContextualGroundingPolicyConfigFilterSensitiveLog: (
  obj: GuardrailContextualGroundingPolicyConfig
) => any;
export declare const GuardrailTopicsTierConfigFilterSensitiveLog: (
  obj: GuardrailTopicsTierConfig
) => any;
export declare const GuardrailTopicConfigFilterSensitiveLog: (
  obj: GuardrailTopicConfig
) => any;
export declare const GuardrailTopicPolicyConfigFilterSensitiveLog: (
  obj: GuardrailTopicPolicyConfig
) => any;

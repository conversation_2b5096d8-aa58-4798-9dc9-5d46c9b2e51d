/**
 * API 使用示例
 * 演示如何与 AWS Bedrock Agent API 进行交互
 * 
 * 这个文件包含了完整的 API 使用示例，包括：
 * 1. 基本聊天交互
 * 2. 会话管理
 * 3. 自定义参数使用
 * 4. 错误处理
 * 5. 健康检查
 */

import fetch from 'node-fetch'

// 配置
const API_BASE_URL = 'http://localhost:3000'
const API_VERSION = 'v1'

/**
 * 发送 API 请求的辅助函数
 * 
 * @param {string} endpoint - API 端点路径
 * @param {object} options - 请求选项
 * @returns {Promise<object>} API 响应数据
 */
async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE_URL}${endpoint}`
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'Bedrock-Agent-Example/1.0.0'
    }
  }

  const response = await fetch(url, { ...defaultOptions, ...options })
  const data = await response.json()

  if (!response.ok) {
    throw new Error(`API 错误: ${response.status} - ${data.message || data.error}`)
  }

  return data
}

/**
 * 示例 1：基本聊天交互
 * 演示如何发送简单的消息给 AI 助手
 */
export async function basicChatExample() {
  console.log('🤖 基本聊天示例')
  console.log('=' .repeat(50))

  try {
    // 发送一个简单的消息
    const response = await apiRequest('/api/v1/chat', {
      method: 'POST',
      body: JSON.stringify({
        message: '你好！你能帮我了解什么是 AWS Bedrock 吗？',
        userId: 'example-user-1'
      })
    })

    console.log('✅ 聊天响应:')
    console.log(`会话 ID: ${response.data.sessionId}`)
    console.log(`AI 回复: ${response.data.message}`)
    console.log(`使用模型: ${response.data.metadata.modelId}`)
    console.log(`处理时间: ${response.data.metadata.processingTime}ms`)
    console.log(`使用令牌: ${response.data.metadata.tokensUsed}`)

    return response.data.sessionId
  } catch (error) {
    console.error('❌ 错误:', error.message)
    throw error
  }
}

/**
 * 示例 2：带会话管理的对话
 * 演示如何维护多轮对话的上下文
 */
export async function conversationExample() {
  console.log('\n💬 对话示例')
  console.log('=' .repeat(50))

  try {
    let sessionId = null

    // 第一条消息
    const response1 = await apiRequest('/api/v1/chat', {
      method: 'POST',
      body: JSON.stringify({
        message: '我正在计划去日本旅行。你能帮助我吗？',
        userId: 'example-user-2'
      })
    })

    sessionId = response1.data.sessionId
    console.log('👤 用户: 我正在计划去日本旅行。你能帮助我吗？')
    console.log(`🤖 助手: ${response1.data.message}`)

    // 在同一会话中的后续消息
    const response2 = await apiRequest('/api/v1/chat', {
      method: 'POST',
      body: JSON.stringify({
        message: '东京有哪些最值得参观的地方？',
        sessionId: sessionId,
        userId: 'example-user-2'
      })
    })

    console.log('\n👤 用户: 东京有哪些最值得参观的地方？')
    console.log(`🤖 助手: ${response2.data.message}`)

    // 另一个后续问题
    const response3 = await apiRequest('/api/v1/chat', {
      method: 'POST',
      body: JSON.stringify({
        message: '一周的旅行大概需要多少预算？',
        sessionId: sessionId,
        userId: 'example-user-2'
      })
    })

    console.log('\n👤 用户: 一周的旅行大概需要多少预算？')
    console.log(`🤖 助手: ${response3.data.message}`)

    // 获取会话信息
    const sessionInfo = await apiRequest(`/api/v1/chat/session/${sessionId}`)
    console.log('\n📊 会话信息:')
    console.log(`消息数量: ${sessionInfo.data.messageCount}`)
    console.log(`创建时间: ${sessionInfo.data.createdAt}`)
    console.log(`最后活动: ${sessionInfo.data.lastActivity}`)

    return sessionId
  } catch (error) {
    console.error('❌ 错误:', error.message)
    throw error
  }
}

/**
 * 示例 3：自定义参数使用
 * 演示如何使用不同的模型参数来控制 AI 的行为
 */
export async function customParametersExample() {
  console.log('\n⚙️ 自定义参数示例')
  console.log('=' .repeat(50))

  try {
    // 创意写作（高温度值）
    const creativeResponse = await apiRequest('/api/v1/chat', {
      method: 'POST',
      body: JSON.stringify({
        message: '写一个关于机器人学习绘画的短篇创意故事。',
        userId: 'example-user-3',
        temperature: 0.9,        // 高温度值增加创造性
        maxTokens: 500,          // 限制回复长度
        systemPrompt: '你是一个创意写作助手。写出引人入胜、富有想象力的故事，包含生动的描述。'
      })
    })

    console.log('🎨 创意写作（高温度值）:')
    console.log(creativeResponse.data.message)

    // 技术解释（低温度值）
    const technicalResponse = await apiRequest('/api/v1/chat', {
      method: 'POST',
      body: JSON.stringify({
        message: '解释机器学习算法是如何工作的。',
        userId: 'example-user-3',
        temperature: 0.1,        // 低温度值确保准确性
        maxTokens: 300,          // 较短的回复
        systemPrompt: '你是一个技术专家。提供清晰、准确、简洁的解释。'
      })
    })

    console.log('\n🔬 技术解释（低温度值）:')
    console.log(technicalResponse.data.message)

  } catch (error) {
    console.error('❌ 错误:', error.message)
    throw error
  }
}

/**
 * 示例 4：会话管理操作
 * 演示如何管理用户会话，包括创建、查询和清理
 */
export async function sessionManagementExample() {
  console.log('\n📝 会话管理示例')
  console.log('=' .repeat(50))

  try {
    const userId = 'example-user-4'

    // 创建多个会话
    const session1 = await apiRequest('/api/v1/chat', {
      method: 'POST',
      body: JSON.stringify({
        message: '告诉我关于机器学习的知识。',
        userId: userId
      })
    })

    const session2 = await apiRequest('/api/v1/chat', {
      method: 'POST',
      body: JSON.stringify({
        message: '什么是云计算？',
        userId: userId
      })
    })

    console.log(`创建会话 1: ${session1.data.sessionId}`)
    console.log(`创建会话 2: ${session2.data.sessionId}`)

    // 获取用户的所有会话
    const userSessions = await apiRequest(`/api/v1/chat/user/${userId}/sessions`)
    console.log(`\n👤 用户 ${userId} 拥有 ${userSessions.data.count} 个会话:`)
    userSessions.data.sessions.forEach((sessionId, index) => {
      console.log(`  ${index + 1}. ${sessionId}`)
    })

    // 清除一个会话
    await apiRequest(`/api/v1/chat/session/${session1.data.sessionId}`, {
      method: 'DELETE'
    })
    console.log(`\n🗑️ 已清除会话: ${session1.data.sessionId}`)

    // 验证会话已被清除
    try {
      await apiRequest(`/api/v1/chat/session/${session1.data.sessionId}`)
    } catch (error) {
      console.log('✅ 会话已成功清除（预期的 404 错误）')
    }

  } catch (error) {
    console.error('❌ 错误:', error.message)
    throw error
  }
}

/**
 * 示例 5：健康检查和监控
 * 演示如何检查应用程序的健康状态和获取指标
 */
export async function healthCheckExample() {
  console.log('\n🏥 健康检查示例')
  console.log('=' .repeat(50))

  try {
    // 基本健康检查
    const basicHealth = await apiRequest('/health')
    console.log('🟢 基本健康检查:')
    console.log(`状态: ${basicHealth.status}`)
    console.log(`运行时间: ${basicHealth.uptime}秒`)

    // 详细健康检查
    const detailedHealth = await apiRequest('/api/v1/health')
    console.log('\n🔍 详细健康检查:')
    console.log(`总体状态: ${detailedHealth.status}`)
    console.log(`响应时间: ${detailedHealth.responseTime}`)
    console.log(`环境: ${detailedHealth.environment}`)
    console.log('服务检查:')
    Object.entries(detailedHealth.checks).forEach(([service, check]) => {
      console.log(`  ${service}: ${check.status}`)
    })

    // 应用程序指标
    const metrics = await apiRequest('/api/v1/metrics')
    console.log('\n📊 应用程序指标:')
    console.log(`内存使用: ${Math.round(metrics.memory.heapUsed / 1024 / 1024)}MB`)
    console.log(`进程 ID: ${metrics.process.pid}`)
    console.log(`Node 版本: ${metrics.process.version}`)

  } catch (error) {
    console.error('❌ 错误:', error.message)
    throw error
  }
}

/**
 * 示例 6：错误处理演示
 * 演示各种错误场景和正确的处理方式
 */
export async function errorHandlingExample() {
  console.log('\n⚠️ 错误处理示例')
  console.log('=' .repeat(50))

  // 测试各种错误场景
  const errorTests = [
    {
      name: '空消息',
      request: { message: '', userId: 'test-user' }
    },
    {
      name: '消息过长',
      request: { message: 'a'.repeat(10001), userId: 'test-user' }
    },
    {
      name: '无效会话 ID',
      request: { message: '你好', sessionId: 'invalid-uuid', userId: 'test-user' }
    },
    {
      name: '无效温度值',
      request: { message: '你好', temperature: 2.0, userId: 'test-user' }
    }
  ]

  for (const test of errorTests) {
    try {
      await apiRequest('/api/v1/chat', {
        method: 'POST',
        body: JSON.stringify(test.request)
      })
      console.log(`❌ ${test.name}: 预期错误但得到成功响应`)
    } catch (error) {
      console.log(`✅ ${test.name}: ${error.message}`)
    }
  }
}

/**
 * 运行所有示例
 * 按顺序执行所有的 API 使用示例
 */
export async function runAllExamples() {
  console.log('🚀 AWS Bedrock Agent API 示例')
  console.log('=' .repeat(60))

  try {
    await basicChatExample()
    await conversationExample()
    await customParametersExample()
    await sessionManagementExample()
    await healthCheckExample()
    await errorHandlingExample()

    console.log('\n✅ 所有示例执行完成！')
    console.log('\n📝 使用提示:')
    console.log('1. 确保应用程序正在运行 (npm start)')
    console.log('2. 检查 .env 文件中的 AWS 配置')
    console.log('3. 确认 Bedrock 模型访问权限')
    console.log('4. 查看日志文件了解详细信息')
  } catch (error) {
    console.error('\n❌ 示例执行失败:', error.message)
    console.log('\n🔧 故障排除建议:')
    console.log('1. 检查应用程序是否正在运行')
    console.log('2. 验证 AWS 凭证配置')
    console.log('3. 确认网络连接')
    console.log('4. 查看应用程序日志')
    process.exit(1)
  }
}

// 如果直接运行此文件，则执行所有示例
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllExamples()
}

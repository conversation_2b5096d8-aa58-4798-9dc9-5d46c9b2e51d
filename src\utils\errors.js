/**
 * 自定义错误类和错误处理工具
 * 为应用程序提供结构化的错误处理
 *
 * 这个文件包含：
 * 1. 各种自定义错误类型
 * 2. 错误格式化和日志记录工具
 * 3. AWS 错误处理适配器
 * 4. 全局错误处理设置
 */

import { logger } from './logger.js'

/**
 * 应用程序基础错误类
 *
 * 所有自定义错误都继承自这个基类
 * 提供统一的错误格式和属性
 */
export class AppError extends Error {
  /**
   * 构造函数
   *
   * @param {string} message - 错误消息
   * @param {number} statusCode - HTTP 状态码
   * @param {string} code - 错误代码
   * @param {object} details - 错误详细信息
   */
  constructor(message, statusCode = 500, code = 'INTERNAL_ERROR', details = null) {
    super(message)
    this.name = this.constructor.name        // 错误类名
    this.statusCode = statusCode             // HTTP 状态码
    this.code = code                         // 应用程序错误代码
    this.details = details                   // 额外的错误详情
    this.timestamp = new Date().toISOString() // 错误发生时间
    this.isOperational = true                // 标记为可操作的错误（非编程错误）

    // 捕获堆栈跟踪，排除构造函数本身
    Error.captureStackTrace(this, this.constructor)
  }

  /**
   * 将错误对象转换为 JSON 格式
   * 用于 API 响应和日志记录
   *
   * @returns {object} JSON 格式的错误信息
   */
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      statusCode: this.statusCode,
      details: this.details,
      timestamp: this.timestamp
    }
  }
}

/**
 * Validation error class
 */
export class ValidationError extends AppError {
  constructor(message, details = null) {
    super(message, 400, 'VALIDATION_ERROR', details)
  }
}

/**
 * Authentication error class
 */
export class AuthenticationError extends AppError {
  constructor(message = 'Authentication failed') {
    super(message, 401, 'AUTHENTICATION_ERROR')
  }
}

/**
 * Authorization error class
 */
export class AuthorizationError extends AppError {
  constructor(message = 'Access denied') {
    super(message, 403, 'AUTHORIZATION_ERROR')
  }
}

/**
 * Not found error class
 */
export class NotFoundError extends AppError {
  constructor(resource = 'Resource') {
    super(`${resource} not found`, 404, 'NOT_FOUND_ERROR')
  }
}

/**
 * Rate limit error class
 */
export class RateLimitError extends AppError {
  constructor(message = 'Rate limit exceeded') {
    super(message, 429, 'RATE_LIMIT_ERROR')
  }
}

/**
 * AWS service error class
 */
export class AWSServiceError extends AppError {
  constructor(message, awsError = null) {
    const statusCode = awsError?.statusCode || 500
    const code = awsError?.code || 'AWS_SERVICE_ERROR'
    
    super(message, statusCode, code, {
      awsErrorCode: awsError?.code,
      awsErrorMessage: awsError?.message,
      awsRequestId: awsError?.requestId
    })
  }
}

/**
 * Bedrock specific error class
 */
export class BedrockError extends AppError {
  constructor(message, modelId = null, operation = null) {
    super(message, 500, 'BEDROCK_ERROR', {
      modelId,
      operation
    })
  }
}

/**
 * Session error class
 */
export class SessionError extends AppError {
  constructor(message, sessionId = null) {
    super(message, 400, 'SESSION_ERROR', {
      sessionId
    })
  }
}

/**
 * Configuration error class
 */
export class ConfigurationError extends AppError {
  constructor(message, configKey = null) {
    super(message, 500, 'CONFIGURATION_ERROR', {
      configKey
    })
  }
}

/**
 * External service error class
 */
export class ExternalServiceError extends AppError {
  constructor(message, service = null, statusCode = 502) {
    super(message, statusCode, 'EXTERNAL_SERVICE_ERROR', {
      service
    })
  }
}

/**
 * Timeout error class
 */
export class TimeoutError extends AppError {
  constructor(message = 'Operation timed out', timeout = null) {
    super(message, 408, 'TIMEOUT_ERROR', {
      timeout
    })
  }
}

/**
 * Error handler utility functions
 */

/**
 * Check if error is operational (expected) or programming error
 * @param {Error} error - Error to check
 * @returns {boolean} True if operational error
 */
export function isOperationalError(error) {
  if (error instanceof AppError) {
    return error.isOperational
  }
  return false
}

/**
 * Format error for API response
 * @param {Error} error - Error to format
 * @param {boolean} includeStack - Whether to include stack trace
 * @returns {object} Formatted error response
 */
export function formatErrorResponse(error, includeStack = false) {
  const response = {
    success: false,
    error: error.name || 'Error',
    message: error.message,
    code: error.code || 'UNKNOWN_ERROR',
    timestamp: new Date().toISOString()
  }

  // Add details if available
  if (error.details) {
    response.details = error.details
  }

  // Add stack trace in development
  if (includeStack && error.stack) {
    response.stack = error.stack
  }

  return response
}

/**
 * Log error with appropriate level
 * @param {Error} error - Error to log
 * @param {object} context - Additional context
 */
export function logError(error, context = {}) {
  const errorInfo = {
    name: error.name,
    message: error.message,
    code: error.code,
    statusCode: error.statusCode,
    stack: error.stack,
    ...context
  }

  if (isOperationalError(error)) {
    // Operational errors are expected, log as warning
    logger.warn('Operational error occurred', errorInfo)
  } else {
    // Programming errors are unexpected, log as error
    logger.error('Programming error occurred', errorInfo)
  }
}

/**
 * Handle AWS SDK errors
 * @param {Error} awsError - AWS SDK error
 * @returns {AppError} Formatted application error
 */
export function handleAWSError(awsError) {
  const message = awsError.message || 'AWS service error'
  
  // Map common AWS errors to appropriate status codes
  const errorMappings = {
    'AccessDenied': 403,
    'InvalidParameterValue': 400,
    'ResourceNotFound': 404,
    'ThrottlingException': 429,
    'ServiceUnavailable': 503,
    'InternalServerError': 500
  }

  const statusCode = errorMappings[awsError.code] || 500
  
  return new AWSServiceError(message, {
    ...awsError,
    statusCode
  })
}

/**
 * Handle validation errors from Joi
 * @param {object} joiError - Joi validation error
 * @returns {ValidationError} Formatted validation error
 */
export function handleValidationError(joiError) {
  const details = joiError.details.map(detail => ({
    field: detail.path.join('.'),
    message: detail.message,
    value: detail.context?.value
  }))

  return new ValidationError('Validation failed', details)
}

/**
 * Async error wrapper for route handlers
 * @param {function} fn - Async function to wrap
 * @returns {function} Wrapped function with error handling
 */
export function asyncErrorHandler(fn) {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}

/**
 * Create error response middleware
 * @param {boolean} isDevelopment - Whether in development mode
 * @returns {function} Express error middleware
 */
export function createErrorMiddleware(isDevelopment = false) {
  return (error, req, res, next) => {
    // Log the error
    logError(error, {
      url: req.url,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      requestId: req.requestId
    })

    // Handle specific error types
    let appError = error

    if (!(error instanceof AppError)) {
      // Convert non-AppError to AppError
      if (error.name === 'ValidationError') {
        appError = handleValidationError(error)
      } else if (error.code && error.code.startsWith('AWS')) {
        appError = handleAWSError(error)
      } else {
        appError = new AppError(
          isDevelopment ? error.message : 'Internal server error',
          error.statusCode || 500
        )
      }
    }

    // Send error response
    const statusCode = appError.statusCode || 500
    const errorResponse = formatErrorResponse(appError, isDevelopment)

    res.status(statusCode).json(errorResponse)
  }
}

/**
 * Global error handlers for uncaught exceptions
 */
export function setupGlobalErrorHandlers() {
  process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', {
      error: error.message,
      stack: error.stack
    })
    
    // Give time for logging then exit
    setTimeout(() => {
      process.exit(1)
    }, 1000)
  })

  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection:', {
      reason: reason instanceof Error ? reason.message : reason,
      stack: reason instanceof Error ? reason.stack : undefined,
      promise: promise.toString()
    })
    
    // Give time for logging then exit
    setTimeout(() => {
      process.exit(1)
    }, 1000)
  })
}

export default {
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  RateLimitError,
  AWSServiceError,
  BedrockError,
  SessionError,
  ConfigurationError,
  ExternalServiceError,
  TimeoutError,
  isOperationalError,
  formatErrorResponse,
  logError,
  handleAWSError,
  handleValidationError,
  asyncErrorHandler,
  createErrorMiddleware,
  setupGlobalErrorHandlers
}

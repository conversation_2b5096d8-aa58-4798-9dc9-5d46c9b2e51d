# 部署指南

本指南涵盖了 AWS Bedrock Agent 应用程序的各种部署选项。

## 🐳 Docker 部署

### 本地 Docker 部署

1. **构建 Docker 镜像**
   ```bash
   docker build -t aws-bedrock-agent .
   ```

2. **使用环境文件运行**
   ```bash
   docker run -d \
     --name bedrock-agent \
     --env-file .env \
     -p 3000:3000 \
     aws-bedrock-agent
   ```

3. **使用 Docker Compose 运行**
   ```bash
   docker-compose up -d
   ```

### Docker Compose 服务

`docker-compose.yml` 包括：
- **bedrock-agent**：主应用程序
- **redis**：会话存储（可选）
- **nginx**：反向代理（可选）
- **prometheus**：指标收集（可选）
- **grafana**：指标可视化（可选）

## ☁️ AWS 部署选项

### 1. Amazon ECS（推荐）

#### 前置要求
- 已配置 AWS CLI
- 已安装 ECS CLI
- Docker 镜像已推送到 ECR

#### 设置步骤

1. **创建 ECR 仓库**
   ```bash
   aws ecr create-repository --repository-name aws-bedrock-agent
   ```

2. **构建并推送镜像**
   ```bash
   # 获取登录令牌
   aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin ************.dkr.ecr.us-east-1.amazonaws.com

   # 构建并标记镜像
   docker build -t aws-bedrock-agent .
   docker tag aws-bedrock-agent:latest ************.dkr.ecr.us-east-1.amazonaws.com/aws-bedrock-agent:latest

   # 推送镜像
   docker push ************.dkr.ecr.us-east-1.amazonaws.com/aws-bedrock-agent:latest
   ```

3. **创建 ECS 任务定义**
   ```json
   {
     "family": "bedrock-agent-task",
     "networkMode": "awsvpc",
     "requiresCompatibilities": ["FARGATE"],
     "cpu": "256",
     "memory": "512",
     "executionRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionRole",
     "taskRoleArn": "arn:aws:iam::************:role/BedrockAgentRole",
     "containerDefinitions": [
       {
         "name": "bedrock-agent",
         "image": "************.dkr.ecr.us-east-1.amazonaws.com/aws-bedrock-agent:latest",
         "portMappings": [
           {
             "containerPort": 3000,
             "protocol": "tcp"
           }
         ],
         "environment": [
           {
             "name": "NODE_ENV",
             "value": "production"
           },
           {
             "name": "AWS_REGION",
             "value": "us-east-1"
           }
         ],
         "logConfiguration": {
           "logDriver": "awslogs",
           "options": {
             "awslogs-group": "/ecs/bedrock-agent",
             "awslogs-region": "us-east-1",
             "awslogs-stream-prefix": "ecs"
           }
         },
         "healthCheck": {
           "command": ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"],
           "interval": 30,
           "timeout": 5,
           "retries": 3
         }
       }
     ]
   }
   ```

4. **创建 ECS 服务**
   ```bash
   aws ecs create-service \
     --cluster bedrock-agent-cluster \
     --service-name bedrock-agent-service \
     --task-definition bedrock-agent-task \
     --desired-count 2 \
     --launch-type FARGATE \
     --network-configuration "awsvpcConfiguration={subnets=[subnet-12345,subnet-67890],securityGroups=[sg-abcdef],assignPublicIp=ENABLED}"
   ```

### 2. Amazon EC2

#### 启动模板
```json
{
  "LaunchTemplateName": "bedrock-agent-template",
  "LaunchTemplateData": {
    "ImageId": "ami-0abcdef1234567890",
    "InstanceType": "t3.micro",
    "IamInstanceProfile": {
      "Name": "BedrockAgentInstanceProfile"
    },
    "SecurityGroupIds": ["sg-abcdef123"],
    "UserData": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  }
}
```

### 3. AWS Lambda（无服务器）

对于无服务器部署，需要适配应用程序：

1. **安装 Serverless Framework**
   ```bash
   npm install -g serverless
   ```

2. **创建 serverless.yml**
   ```yaml
   service: aws-bedrock-agent
   
   provider:
     name: aws
     runtime: nodejs18.x
     region: us-east-1
     iamRoleStatements:
       - Effect: Allow
         Action:
           - bedrock:InvokeModel
           - bedrock:InvokeModelWithResponseStream
         Resource: "*"
   
   functions:
     api:
       handler: src/lambda.handler
       events:
         - http:
             path: /{proxy+}
             method: ANY
             cors: true
       timeout: 30
       memorySize: 512
   ```

3. **创建 Lambda 处理程序**
   ```javascript
   // src/lambda.js
   import serverless from 'serverless-http'
   import app from './index.js'
   
   export const handler = serverless(app)
   ```

### 4. Amazon EKS（Kubernetes）

#### Kubernetes 清单

1. **部署**
   ```yaml
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: bedrock-agent
   spec:
     replicas: 3
     selector:
       matchLabels:
         app: bedrock-agent
     template:
       metadata:
         labels:
           app: bedrock-agent
       spec:
         serviceAccountName: bedrock-agent-sa
         containers:
         - name: bedrock-agent
           image: ************.dkr.ecr.us-east-1.amazonaws.com/aws-bedrock-agent:latest
           ports:
           - containerPort: 3000
           env:
           - name: NODE_ENV
             value: "production"
           - name: AWS_REGION
             value: "us-east-1"
           livenessProbe:
             httpGet:
               path: /health
               port: 3000
             initialDelaySeconds: 30
             periodSeconds: 10
           readinessProbe:
             httpGet:
               path: /ready
               port: 3000
             initialDelaySeconds: 5
             periodSeconds: 5
   ```

2. **服务**
   ```yaml
   apiVersion: v1
   kind: Service
   metadata:
     name: bedrock-agent-service
   spec:
     selector:
       app: bedrock-agent
     ports:
     - port: 80
       targetPort: 3000
     type: LoadBalancer
   ```

## 🔧 环境配置

### 生产环境变量

```env
# 必需
NODE_ENV=production
AWS_REGION=us-east-1
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0

# 服务器
PORT=3000
HOST=0.0.0.0

# 安全
CORS_ORIGIN=https://yourdomain.com
API_RATE_LIMIT_MAX_REQUESTS=1000

# 日志
LOG_LEVEL=warn
LOG_FILE_PATH=/app/logs/app.log

# 会话管理
SESSION_STORE_TYPE=redis
REDIS_URL=redis://redis:6379

# 监控
ENABLE_METRICS=true
CLOUDWATCH_LOG_GROUP=/aws/bedrock-agent
```

## 📊 监控和日志

### CloudWatch 集成

1. **创建日志组**
   ```bash
   aws logs create-log-group --log-group-name /aws/bedrock-agent
   ```

2. **配置应用程序日志**
   ```javascript
   // src/utils/cloudwatch.js
   import { CloudWatchLogsClient, PutLogEventsCommand } from '@aws-sdk/client-cloudwatch-logs'
   
   export class CloudWatchLogger {
     constructor(logGroupName, logStreamName) {
       this.client = new CloudWatchLogsClient({ region: process.env.AWS_REGION })
       this.logGroupName = logGroupName
       this.logStreamName = logStreamName
     }
   
     async log(message, level = 'INFO') {
       const params = {
         logGroupName: this.logGroupName,
         logStreamName: this.logStreamName,
         logEvents: [{
           timestamp: Date.now(),
           message: JSON.stringify({ level, message, timestamp: new Date().toISOString() })
         }]
       }
   
       try {
         await this.client.send(new PutLogEventsCommand(params))
       } catch (error) {
         console.error('CloudWatch 日志记录错误:', error)
       }
     }
   }
   ```

## 🔒 安全考虑

### 1. 网络安全
- 使用带有私有子网的 VPC
- 配置具有最小必需访问权限的安全组
- 使用带有 SSL 终止的应用程序负载均衡器

### 2. 容器安全
- 在容器中使用非 root 用户
- 扫描镜像以查找漏洞
- 保持基础镜像更新

### 3. 机密管理
- 使用 AWS Secrets Manager 或 Parameter Store
- 永远不要在容器镜像中包含机密
- 定期轮换机密

### 4. 访问控制
- 使用 IAM 角色而不是访问密钥
- 实施最小权限原则
- 启用 AWS CloudTrail 进行审计

## 🚀 CI/CD 流水线

### GitHub Actions 示例

```yaml
name: 部署到 AWS

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: 配置 AWS 凭证
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
    
    - name: 登录到 Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1
    
    - name: 构建并推送镜像
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: aws-bedrock-agent
        IMAGE_TAG: ${{ github.sha }}
      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
    
    - name: 部署到 ECS
      run: |
        aws ecs update-service --cluster bedrock-agent-cluster --service bedrock-agent-service --force-new-deployment
```

## 📈 扩展考虑

### 水平扩展
- 使用应用程序负载均衡器
- 配置自动扩展组
- 实施健康检查

### 垂直扩展
- 监控 CPU 和内存使用情况
- 根据需要调整容器资源
- 使用适当的实例类型

### 数据库扩展
- 使用 Redis 集群进行会话存储
- 实施连接池
- 考虑为高读取工作负载使用只读副本

## 🔄 备份和灾难恢复

### 数据备份
- 定期快照持久卷
- 备份配置文件
- 如需要，导出会话数据

### 灾难恢复
- 多区域部署
- 自动故障转移程序
- 定期灾难恢复测试

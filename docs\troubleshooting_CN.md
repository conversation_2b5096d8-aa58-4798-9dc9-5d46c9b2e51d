# 故障排除指南

本指南帮助您解决 AWS Bedrock Agent 应用程序的常见问题。

## 🚨 常见错误和解决方案

### 1. AWS 连接问题

#### 错误：`AccessDeniedException`
```
错误消息：User: arn:aws:iam::123456789012:user/bedrock-user is not authorized to perform: bedrock:InvokeModel
```

**原因**：IAM 用户没有调用 Bedrock 模型的权限

**解决方案**：
1. 检查 IAM 策略是否正确附加
2. 确认策略包含必要的 Bedrock 权限
3. 验证资源 ARN 是否正确

```bash
# 检查当前用户身份
aws sts get-caller-identity

# 检查用户的策略
aws iam list-attached-user-policies --user-name bedrock-user
```

#### 错误：`InvalidSignatureException`
```
错误消息：The request signature we calculated does not match the signature you provided
```

**原因**：AWS 凭证不正确或已过期

**解决方案**：
1. 检查 `.env` 文件中的 AWS 凭证
2. 确认访问密钥和秘密密钥正确
3. 检查系统时间是否正确

```bash
# 重新配置 AWS 凭证
aws configure

# 测试凭证
aws sts get-caller-identity
```

#### 错误：`ValidationException: Model not found`
```
错误消息：The model ID 'anthropic.claude-3-sonnet-20240229-v1:0' was not found
```

**原因**：模型在当前区域不可用或未请求访问权限

**解决方案**：
1. 在 Bedrock 控制台请求模型访问权限
2. 检查模型在当前区域是否可用
3. 等待访问请求批准（通常几分钟）

### 2. 应用程序启动问题

#### 错误：`Missing required environment variables`
```
错误消息：Missing required environment variables: AWS_REGION
```

**原因**：缺少必需的环境变量

**解决方案**：
1. 检查 `.env` 文件是否存在
2. 确认所有必需变量都已设置
3. 重启应用程序

```bash
# 检查环境变量
echo $AWS_REGION
echo $AWS_ACCESS_KEY_ID

# 从示例文件复制
cp .env.example .env
# 然后编辑 .env 文件
```

#### 错误：`EADDRINUSE: address already in use`
```
错误消息：Error: listen EADDRINUSE: address already in use :::3000
```

**原因**：端口 3000 已被其他进程占用

**解决方案**：
1. 更改端口号
2. 或停止占用端口的进程

```bash
# 查找占用端口的进程
netstat -tulpn | grep 3000
# 或使用 lsof
lsof -i :3000

# 更改端口（在 .env 文件中）
PORT=3001

# 或杀死占用端口的进程
kill -9 <进程ID>
```

### 3. API 请求问题

#### 错误：`400 Bad Request - Validation failed`
```json
{
  "success": false,
  "error": "验证失败",
  "details": ["消息不能为空"]
}
```

**原因**：请求数据格式不正确

**解决方案**：
1. 检查请求体格式
2. 确认必需字段都已提供
3. 验证数据类型正确

**正确的请求格式**：
```json
{
  "message": "你好，你能帮助我吗？",
  "userId": "user123",
  "temperature": 0.7,
  "maxTokens": 1000
}
```

#### 错误：`429 Too Many Requests`
```json
{
  "success": false,
  "error": "Too many requests",
  "message": "Rate limit exceeded"
}
```

**原因**：超过了速率限制

**解决方案**：
1. 减少请求频率
2. 实施客户端重试逻辑
3. 调整速率限制配置

```env
# 在 .env 文件中调整速率限制
API_RATE_LIMIT_MAX_REQUESTS=200
API_RATE_LIMIT_WINDOW_MS=900000
```

### 4. Docker 部署问题

#### 错误：Docker 构建失败
```
ERROR [4/4] RUN npm ci --only=production
```

**原因**：依赖安装失败

**解决方案**：
1. 检查网络连接
2. 清理 Docker 缓存
3. 更新 package.json

```bash
# 清理 Docker 缓存
docker system prune -a

# 重新构建镜像
docker build --no-cache -t aws-bedrock-agent .
```

#### 错误：容器启动失败
```
Error: Cannot find module './config/index.js'
```

**原因**：文件路径或权限问题

**解决方案**：
1. 检查 Dockerfile 中的 COPY 指令
2. 确认文件权限正确
3. 验证工作目录设置

```dockerfile
# 确保正确复制文件
COPY --chown=nodejs:nodejs src/ ./src/
```

### 5. 性能问题

#### 问题：响应时间过长
**症状**：API 请求需要很长时间才能返回

**可能原因**：
1. 模型选择不当
2. 请求令牌数过多
3. 网络延迟

**解决方案**：
1. 选择更快的模型（如 Claude 3 Haiku）
2. 减少 maxTokens 设置
3. 检查网络连接

```env
# 使用更快的模型
BEDROCK_MODEL_ID=anthropic.claude-3-haiku-20240307-v1:0
```

#### 问题：内存使用过高
**症状**：应用程序内存使用不断增长

**可能原因**：
1. 会话数据未正确清理
2. 内存泄漏

**解决方案**：
1. 调整会话清理间隔
2. 限制最大会话数

```env
# 更频繁的会话清理
SESSION_CLEANUP_INTERVAL=60000
# 限制最大会话数
MAX_SESSIONS=500
```

## 🔍 调试工具和命令

### 1. 检查应用程序状态

```bash
# 检查健康状态
curl http://localhost:3000/health

# 检查详细健康信息
curl http://localhost:3000/api/v1/health

# 检查应用程序指标
curl http://localhost:3000/api/v1/metrics
```

### 2. 查看日志

```bash
# 查看应用程序日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log

# 使用 Docker 查看日志
docker logs bedrock-agent

# 实时查看 Docker 日志
docker logs -f bedrock-agent
```

### 3. AWS 调试命令

```bash
# 测试 AWS 连接
aws sts get-caller-identity

# 列出可用的 Bedrock 模型
aws bedrock list-foundation-models --region us-east-1

# 测试模型调用
aws bedrock-runtime invoke-model \
  --model-id anthropic.claude-3-sonnet-20240229-v1:0 \
  --body '{"prompt":"Human: 测试\n\nAssistant:","max_tokens_to_sample":100}' \
  --region us-east-1 \
  output.json

# 查看输出
cat output.json
```

### 4. 网络调试

```bash
# 检查端口是否开放
telnet localhost 3000

# 检查 DNS 解析
nslookup bedrock-runtime.us-east-1.amazonaws.com

# 测试网络连接
ping bedrock-runtime.us-east-1.amazonaws.com
```

## 📊 监控和警报

### 1. 设置 CloudWatch 警报

```bash
# CPU 使用率警报
aws cloudwatch put-metric-alarm \
  --alarm-name "BedrockAgent-HighCPU" \
  --alarm-description "Bedrock Agent CPU 使用率过高" \
  --metric-name CPUUtilization \
  --namespace AWS/ECS \
  --statistic Average \
  --period 300 \
  --threshold 80 \
  --comparison-operator GreaterThanThreshold

# 错误率警报
aws cloudwatch put-metric-alarm \
  --alarm-name "BedrockAgent-HighErrorRate" \
  --alarm-description "Bedrock Agent 错误率过高" \
  --metric-name ErrorRate \
  --namespace BedrockAgent \
  --statistic Average \
  --period 300 \
  --threshold 5 \
  --comparison-operator GreaterThanThreshold
```

### 2. 日志分析

```bash
# 搜索错误日志
grep "ERROR" logs/app.log

# 统计错误类型
grep "ERROR" logs/app.log | awk '{print $4}' | sort | uniq -c

# 查看最近的错误
tail -n 100 logs/error.log | grep "$(date +%Y-%m-%d)"
```

## 🆘 获取帮助

### 1. 检查文档
- [README_CN.md](../README_CN.md) - 完整设置指南
- [aws-setup_CN.md](aws-setup_CN.md) - AWS 配置指南
- [deployment_CN.md](deployment_CN.md) - 部署指南

### 2. 启用调试模式

```env
# 在 .env 文件中启用调试
LOG_LEVEL=debug
NODE_ENV=development
```

### 3. 收集诊断信息

创建诊断脚本：

```bash
#!/bin/bash
# diagnostic.sh - 收集诊断信息

echo "=== 系统信息 ==="
uname -a
node --version
npm --version

echo "=== 环境变量 ==="
env | grep -E "(AWS|BEDROCK|NODE)" | sort

echo "=== 网络连接 ==="
curl -s http://localhost:3000/health || echo "健康检查失败"

echo "=== 最近的错误 ==="
tail -n 20 logs/error.log

echo "=== 进程信息 ==="
ps aux | grep node
```

### 4. 联系支持

如果问题仍然存在：

1. 收集诊断信息
2. 记录错误消息和步骤
3. 检查 GitHub Issues
4. 创建新的 Issue 并包含：
   - 错误消息
   - 环境信息
   - 重现步骤
   - 诊断信息

## 🔧 预防措施

### 1. 定期维护

```bash
# 定期清理日志
find logs/ -name "*.log" -mtime +7 -delete

# 更新依赖
npm audit
npm update

# 检查 Docker 镜像更新
docker pull node:18-alpine
```

### 2. 监控设置

- 设置 CloudWatch 警报
- 配置日志轮转
- 监控磁盘空间
- 跟踪 API 使用情况

### 3. 备份策略

- 定期备份配置文件
- 导出重要会话数据
- 保存 Docker 镜像版本
- 记录配置更改

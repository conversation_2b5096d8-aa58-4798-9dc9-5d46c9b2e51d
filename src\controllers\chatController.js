/**
 * 聊天控制器
 * 处理与聊天相关的 API 端点
 *
 * 这个控制器负责：
 * 1. 接收用户的聊天请求
 * 2. 验证请求数据
 * 3. 调用 Bedrock Agent 处理消息
 * 4. 返回 AI 的响应
 * 5. 管理会话状态
 */

import { BedrockAgent } from '../services/bedrockAgent.js'
import { logger, logBedrockInvocation, logError } from '../utils/logger.js'
import { validateChatRequest } from '../utils/validation.js'

// 初始化 Bedrock 代理实例
// 这个实例将处理所有的 AI 对话请求
const bedrockAgent = new BedrockAgent()

/**
 * 处理聊天消息
 * POST /api/v1/chat
 *
 * 这是主要的聊天端点，用户通过这个接口与 AI 进行对话
 *
 * 请求体格式：
 * {
 *   "message": "用户消息",
 *   "sessionId": "会话ID（可选）",
 *   "userId": "用户ID",
 *   "temperature": 0.7,  // 可选，控制回复的创造性
 *   "maxTokens": 1000    // 可选，限制回复长度
 * }
 *
 * @param {object} req - Express 请求对象
 * @param {object} res - Express 响应对象
 */
export async function processMessage(req, res) {
  // 记录请求开始时间，用于计算处理时长
  const startTime = Date.now()

  try {
    // 验证请求数据
    // 检查消息内容、格式、长度等是否符合要求
    const { error, value } = validateChatRequest(req.body)
    if (error) {
      return res.status(400).json({
        success: false,
        error: '验证失败',
        details: error.details.map(detail => detail.message)
      })
    }

    const {
      message,
      sessionId,
      streaming = false,
      systemPrompt,
      temperature,
      maxTokens,
      userId = 'anonymous'
    } = value

    // Log the request
    req.logger?.info('Processing chat message', {
      messageLength: message.length,
      sessionId,
      streaming,
      userId
    })

    // Process message with Bedrock agent
    const response = await bedrockAgent.processMessage(message, {
      sessionId,
      streaming,
      systemPrompt,
      temperature,
      maxTokens,
      userId
    })

    const processingTime = Date.now() - startTime

    // Log Bedrock invocation
    logBedrockInvocation(response.metadata.modelId, {
      sessionId: response.sessionId,
      tokensUsed: response.metadata.tokensUsed,
      processingTime,
      streaming: response.metadata.streaming
    })

    // Return successful response
    res.status(200).json({
      success: true,
      data: {
        sessionId: response.sessionId,
        message: response.response,
        metadata: {
          ...response.metadata,
          processingTime
        }
      }
    })

  } catch (error) {
    const processingTime = Date.now() - startTime
    
    logError(error, {
      endpoint: '/chat',
      processingTime,
      requestBody: req.body
    })

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to process message',
      metadata: {
        processingTime
      }
    })
  }
}

/**
 * Get session information
 * GET /api/v1/chat/session/:sessionId
 */
export async function getSessionInfo(req, res) {
  try {
    const { sessionId } = req.params

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: 'Session ID is required'
      })
    }

    const sessionInfo = await bedrockAgent.getSessionInfo(sessionId)

    if (!sessionInfo) {
      return res.status(404).json({
        success: false,
        error: 'Session not found'
      })
    }

    res.status(200).json({
      success: true,
      data: sessionInfo
    })

  } catch (error) {
    logError(error, {
      endpoint: '/chat/session',
      sessionId: req.params.sessionId
    })

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to retrieve session information'
    })
  }
}

/**
 * Clear session history
 * DELETE /api/v1/chat/session/:sessionId
 */
export async function clearSession(req, res) {
  try {
    const { sessionId } = req.params

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: 'Session ID is required'
      })
    }

    const success = await bedrockAgent.clearSession(sessionId)

    if (!success) {
      return res.status(404).json({
        success: false,
        error: 'Session not found'
      })
    }

    req.logger?.info('Session cleared', { sessionId })

    res.status(200).json({
      success: true,
      message: 'Session cleared successfully'
    })

  } catch (error) {
    logError(error, {
      endpoint: '/chat/session/clear',
      sessionId: req.params.sessionId
    })

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to clear session'
    })
  }
}

/**
 * Get user sessions
 * GET /api/v1/chat/user/:userId/sessions
 */
export async function getUserSessions(req, res) {
  try {
    const { userId } = req.params

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      })
    }

    const sessions = await bedrockAgent.getUserSessions(userId)

    res.status(200).json({
      success: true,
      data: {
        userId,
        sessions,
        count: sessions.length
      }
    })

  } catch (error) {
    logError(error, {
      endpoint: '/chat/user/sessions',
      userId: req.params.userId
    })

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to retrieve user sessions'
    })
  }
}

/**
 * Stream chat response (Server-Sent Events)
 * POST /api/v1/chat/stream
 */
export async function streamMessage(req, res) {
  try {
    // Validate request
    const { error, value } = validateChatRequest(req.body)
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: error.details.map(detail => detail.message)
      })
    }

    const {
      message,
      sessionId,
      systemPrompt,
      temperature,
      maxTokens,
      userId = 'anonymous'
    } = value

    // Set up Server-Sent Events
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    })

    // Send initial connection event
    res.write('event: connected\n')
    res.write('data: {"status": "connected"}\n\n')

    try {
      // Process message with streaming enabled
      const response = await bedrockAgent.processMessage(message, {
        sessionId,
        streaming: true,
        systemPrompt,
        temperature,
        maxTokens,
        userId
      })

      // Send the complete response
      res.write('event: message\n')
      res.write(`data: ${JSON.stringify({
        sessionId: response.sessionId,
        message: response.response,
        metadata: response.metadata
      })}\n\n`)

      // Send completion event
      res.write('event: complete\n')
      res.write('data: {"status": "complete"}\n\n')

    } catch (streamError) {
      // Send error event
      res.write('event: error\n')
      res.write(`data: ${JSON.stringify({
        error: 'Processing failed',
        message: streamError.message
      })}\n\n`)
    }

    res.end()

  } catch (error) {
    logError(error, {
      endpoint: '/chat/stream',
      requestBody: req.body
    })

    if (!res.headersSent) {
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to process streaming message'
      })
    }
  }
}

export default {
  processMessage,
  getSessionInfo,
  clearSession,
  getUserSessions,
  streamMessage
}

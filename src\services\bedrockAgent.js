/**
 * Amazon Bedrock Agent 服务
 * 处理与 Amazon Bedrock 基础模型和代理的交互
 *
 * 这个服务是应用程序的核心，负责：
 * 1. 与 AWS Bedrock 服务通信
 * 2. 管理 AI 对话会话
 * 3. 处理不同模型的提示格式
 * 4. 支持流式和非流式响应
 * 5. 跟踪令牌使用情况和成本
 */

import {
  InvokeModelCommand,                    // 用于调用模型的命令（非流式）
  InvokeModelWithResponseStreamCommand   // 用于调用模型的命令（流式）
} from '@aws-sdk/client-bedrock-runtime'
import { v4 as uuidv4 } from 'uuid'      // 用于生成唯一的会话 ID
import { bedrockRuntimeClient, getModelConfig } from '../config/aws.js'
import { config } from '../config/index.js'
import { logger } from '../utils/logger.js'
import { SessionManager } from './sessionManager.js'

/**
 * BedrockAgent 类 - 处理 AI 对话的主要类
 *
 * 这个类封装了所有与 AI 模型交互的逻辑，包括：
 * - 消息处理和响应生成
 * - 会话管理和上下文维护
 * - 不同模型的适配
 * - 流式响应处理
 */
export class BedrockAgent {
  /**
   * 构造函数 - 初始化 Bedrock Agent
   *
   * @param {object} options - 配置选项
   * @param {string} options.modelId - 要使用的模型 ID
   * @param {string} options.systemPrompt - 自定义系统提示
   */
  constructor(options = {}) {
    // 设置要使用的模型 ID，如果未指定则使用配置文件中的默认值
    this.modelId = options.modelId || config.aws.bedrock.modelId

    // 获取模型特定的配置（温度、最大令牌数等）
    this.modelConfig = getModelConfig(this.modelId)

    // 初始化会话管理器，用于维护对话历史
    this.sessionManager = new SessionManager()

    // 设置默认的系统提示，定义 AI 助手的行为
    this.defaultSystemPrompt = options.systemPrompt || this.getDefaultSystemPrompt()
  }

  /**
   * 获取代理的默认系统提示
   *
   * 系统提示定义了 AI 助手的行为准则和个性
   * 这是告诉 AI 如何回应用户的重要指令
   *
   * @returns {string} 默认系统提示文本
   */
  getDefaultSystemPrompt() {
    return `你是一个由 Amazon Bedrock 驱动的有用的 AI 助手。你为用户查询提供准确、有用和安全的响应。

关键准则：
- 在回应中要简洁但全面
- 如果你对某事不确定，请说出来而不是猜测
- 保持专业和友好的语调
- 尊重用户隐私，不存储个人信息
- 在所有交互中遵循道德 AI 原则

当前模型：${this.modelId}
会话管理：已启用`
  }

  /**
   * Process a user message and generate a response
   * @param {string} message - User message
   * @param {object} options - Additional options
   * @returns {Promise<object>} Agent response
   */
  async processMessage(message, options = {}) {
    try {
      const {
        sessionId = uuidv4(),
        streaming = false,
        systemPrompt = this.defaultSystemPrompt,
        temperature = this.modelConfig.temperature,
        maxTokens = this.modelConfig.maxTokens,
        userId = 'anonymous'
      } = options

      // Validate input
      if (!message || typeof message !== 'string') {
        throw new Error('Message must be a non-empty string')
      }

      // Get or create session
      const session = await this.sessionManager.getSession(sessionId, userId)
      
      // Add user message to conversation history
      session.addMessage('user', message)

      // Prepare the conversation context
      const conversationHistory = session.getMessages()
      
      // Build the prompt based on model type
      const prompt = this.buildPrompt(conversationHistory, systemPrompt)

      logger.info(`Processing message for session ${sessionId}`, {
        userId,
        messageLength: message.length,
        modelId: this.modelId,
        streaming
      })

      let response
      if (streaming && this.modelConfig.supportsStreaming) {
        response = await this.invokeModelStreaming(prompt, {
          temperature,
          maxTokens,
          sessionId
        })
      } else {
        response = await this.invokeModel(prompt, {
          temperature,
          maxTokens,
          sessionId
        })
      }

      // Add assistant response to session
      session.addMessage('assistant', response.content)
      
      // Update session
      await this.sessionManager.updateSession(sessionId, session)

      return {
        sessionId,
        response: response.content,
        metadata: {
          modelId: this.modelId,
          tokensUsed: response.tokensUsed || 0,
          processingTime: response.processingTime || 0,
          streaming: streaming && this.modelConfig.supportsStreaming
        }
      }
    } catch (error) {
      logger.error('Error processing message:', error)
      throw new Error(`Failed to process message: ${error.message}`)
    }
  }

  /**
   * Build prompt based on model type and conversation history
   * @param {Array} messages - Conversation history
   * @param {string} systemPrompt - System prompt
   * @returns {string} Formatted prompt
   */
  buildPrompt(messages, systemPrompt) {
    if (this.modelId.includes('anthropic.claude')) {
      return this.buildClaudePrompt(messages, systemPrompt)
    } else if (this.modelId.includes('amazon.titan')) {
      return this.buildTitanPrompt(messages, systemPrompt)
    } else if (this.modelId.includes('meta.llama')) {
      return this.buildLlamaPrompt(messages, systemPrompt)
    } else {
      // Default format
      return this.buildGenericPrompt(messages, systemPrompt)
    }
  }

  /**
   * Build Claude-specific prompt format
   * @param {Array} messages - Conversation history
   * @param {string} systemPrompt - System prompt
   * @returns {string} Claude-formatted prompt
   */
  buildClaudePrompt(messages, systemPrompt) {
    let prompt = `${systemPrompt}\n\n`
    
    messages.forEach(msg => {
      if (msg.role === 'user') {
        prompt += `Human: ${msg.content}\n\n`
      } else if (msg.role === 'assistant') {
        prompt += `Assistant: ${msg.content}\n\n`
      }
    })
    
    prompt += 'Assistant: '
    return prompt
  }

  /**
   * Build Titan-specific prompt format
   * @param {Array} messages - Conversation history
   * @param {string} systemPrompt - System prompt
   * @returns {string} Titan-formatted prompt
   */
  buildTitanPrompt(messages, systemPrompt) {
    let prompt = `${systemPrompt}\n\n`
    
    messages.forEach(msg => {
      prompt += `${msg.role}: ${msg.content}\n`
    })
    
    return prompt
  }

  /**
   * Build Llama-specific prompt format
   * @param {Array} messages - Conversation history
   * @param {string} systemPrompt - System prompt
   * @returns {string} Llama-formatted prompt
   */
  buildLlamaPrompt(messages, systemPrompt) {
    let prompt = `<s>[INST] <<SYS>>\n${systemPrompt}\n<</SYS>>\n\n`
    
    messages.forEach((msg, index) => {
      if (msg.role === 'user') {
        prompt += `${msg.content} [/INST] `
      } else if (msg.role === 'assistant') {
        prompt += `${msg.content} </s><s>[INST] `
      }
    })
    
    return prompt
  }

  /**
   * Build generic prompt format
   * @param {Array} messages - Conversation history
   * @param {string} systemPrompt - System prompt
   * @returns {string} Generic-formatted prompt
   */
  buildGenericPrompt(messages, systemPrompt) {
    let prompt = `System: ${systemPrompt}\n\n`
    
    messages.forEach(msg => {
      prompt += `${msg.role}: ${msg.content}\n`
    })
    
    prompt += 'assistant: '
    return prompt
  }

  /**
   * Invoke model without streaming
   * @param {string} prompt - The formatted prompt
   * @param {object} options - Invocation options
   * @returns {Promise<object>} Model response
   */
  async invokeModel(prompt, options = {}) {
    const startTime = Date.now()
    
    try {
      const requestBody = this.buildRequestBody(prompt, options)
      
      const command = new InvokeModelCommand({
        modelId: this.modelId,
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify(requestBody)
      })

      const response = await bedrockRuntimeClient.send(command)
      const responseBody = JSON.parse(new TextDecoder().decode(response.body))
      
      const processingTime = Date.now() - startTime
      
      return {
        content: this.extractContentFromResponse(responseBody),
        tokensUsed: this.extractTokenUsage(responseBody),
        processingTime
      }
    } catch (error) {
      logger.error('Error invoking model:', error)
      throw new Error(`Model invocation failed: ${error.message}`)
    }
  }

  /**
   * Invoke model with streaming
   * @param {string} prompt - The formatted prompt
   * @param {object} options - Invocation options
   * @returns {Promise<object>} Streaming response
   */
  async invokeModelStreaming(prompt, options = {}) {
    const startTime = Date.now()
    
    try {
      const requestBody = this.buildRequestBody(prompt, options)
      
      const command = new InvokeModelWithResponseStreamCommand({
        modelId: this.modelId,
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify(requestBody)
      })

      const response = await bedrockRuntimeClient.send(command)
      let fullContent = ''
      let tokensUsed = 0

      // Process streaming response
      for await (const chunk of response.body) {
        if (chunk.chunk?.bytes) {
          const chunkData = JSON.parse(new TextDecoder().decode(chunk.chunk.bytes))
          const content = this.extractContentFromResponse(chunkData)
          if (content) {
            fullContent += content
          }
          tokensUsed += this.extractTokenUsage(chunkData) || 0
        }
      }

      const processingTime = Date.now() - startTime
      
      return {
        content: fullContent,
        tokensUsed,
        processingTime
      }
    } catch (error) {
      logger.error('Error invoking streaming model:', error)
      throw new Error(`Streaming model invocation failed: ${error.message}`)
    }
  }

  /**
   * Build request body based on model type
   * @param {string} prompt - The prompt
   * @param {object} options - Request options
   * @returns {object} Request body
   */
  buildRequestBody(prompt, options = {}) {
    const { temperature, maxTokens } = options

    if (this.modelId.includes('anthropic.claude')) {
      return {
        prompt,
        max_tokens_to_sample: maxTokens,
        temperature,
        top_p: this.modelConfig.topP,
        stop_sequences: this.modelConfig.stopSequences
      }
    } else if (this.modelId.includes('amazon.titan')) {
      return {
        inputText: prompt,
        textGenerationConfig: {
          maxTokenCount: maxTokens,
          temperature,
          topP: this.modelConfig.topP,
          stopSequences: this.modelConfig.stopSequences
        }
      }
    } else {
      // Generic format
      return {
        prompt,
        max_tokens: maxTokens,
        temperature,
        top_p: this.modelConfig.topP
      }
    }
  }

  /**
   * Extract content from model response
   * @param {object} responseBody - Response from model
   * @returns {string} Extracted content
   */
  extractContentFromResponse(responseBody) {
    if (responseBody.completion) {
      return responseBody.completion // Claude
    } else if (responseBody.results?.[0]?.outputText) {
      return responseBody.results[0].outputText // Titan
    } else if (responseBody.generation) {
      return responseBody.generation // Llama
    } else if (responseBody.text) {
      return responseBody.text // Generic
    }
    return ''
  }

  /**
   * Extract token usage from model response
   * @param {object} responseBody - Response from model
   * @returns {number} Number of tokens used
   */
  extractTokenUsage(responseBody) {
    if (responseBody.usage?.total_tokens) {
      return responseBody.usage.total_tokens
    } else if (responseBody.inputTextTokenCount && responseBody.results?.[0]?.tokenCount) {
      return responseBody.inputTextTokenCount + responseBody.results[0].tokenCount
    }
    return 0
  }

  /**
   * Get session information
   * @param {string} sessionId - Session ID
   * @returns {Promise<object>} Session information
   */
  async getSessionInfo(sessionId) {
    return await this.sessionManager.getSessionInfo(sessionId)
  }

  /**
   * Clear session history
   * @param {string} sessionId - Session ID
   * @returns {Promise<boolean>} Success status
   */
  async clearSession(sessionId) {
    return await this.sessionManager.clearSession(sessionId)
  }

  /**
   * List all active sessions for a user
   * @param {string} userId - User ID
   * @returns {Promise<Array>} List of session IDs
   */
  async getUserSessions(userId) {
    return await this.sessionManager.getUserSessions(userId)
  }
}

export default BedrockAgent
